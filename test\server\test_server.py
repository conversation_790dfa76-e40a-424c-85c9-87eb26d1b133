#!/usr/bin/env python3
"""
Test script to verify OTA server is working
"""

import requests
import json

SERVER_URL = "http://***********:8080"

def test_status():
    """Test server status endpoint"""
    try:
        response = requests.get(f"{SERVER_URL}/status", timeout=5)
        print(f"Status test: {response.status_code}")
        print(f"Response: {response.json()}")
        return True
    except Exception as e:
        print(f"Status test failed: {e}")
        return False

def test_update_check():
    """Test update check endpoint"""
    try:
        data = {
            "model": "ESP8266_Switch",
            "version": "1.0.0"
        }
        response = requests.post(f"{SERVER_URL}/check-update", 
                               json=data, 
                               headers={"Content-Type": "application/json"},
                               timeout=5)
        print(f"Update check test: {response.status_code}")
        print(f"Response: {response.json()}")
        return True
    except Exception as e:
        print(f"Update check test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing OTA Server...")
    print(f"Server URL: {SERVER_URL}")
    print()
    
    print("1. Testing status endpoint...")
    status_ok = test_status()
    print()
    
    print("2. Testing update check endpoint...")
    update_ok = test_update_check()
    print()
    
    if status_ok and update_ok:
        print("✅ All tests passed! Server is working correctly.")
    else:
        print("❌ Some tests failed. Check server configuration.")
        print("\nTroubleshooting tips:")
        print("- Make sure the server is running")
        print("- Check Windows Firewall settings")
        print("- Verify IP address is correct")
        print("- Try accessing http://***********:8080/status in browser")
