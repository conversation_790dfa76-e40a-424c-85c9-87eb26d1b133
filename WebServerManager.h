#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <EEPROM.h>
#include "WiFiManager.h"
#include "DeviceManager.h"
#include "TouchSensorManager.h"
#include "OTAManager.h"

class WebServerManager
{
private:
    ESP8266WebServer _server;
    WiFiManager *_wifiManager;
    DeviceManager *_deviceManager;
    TouchSensorManager *_touchManager;
    OTAManager *_otaManager;
    bool _serverRunning;
    int _port;

    // State change notification system
    bool _stateChanged;
    unsigned long _lastStateChange;

    // Page throttling variables
    unsigned long _lastPageRequest = 0;
    unsigned long _lastScanRequest = 0;
    const unsigned long PAGE_THROTTLE_MS = 1000; // 1 second between page requests
    const unsigned long SCAN_THROTTLE_MS = 5000; // 5 seconds between scans
    String _currentRequestingIP = "";

    // HTML templates stored in PROGMEM for memory efficiency
    static const char htmlHeader[] PROGMEM;
    static const char htmlFooter[] PROGMEM;

    // Page-specific templates in PROGMEM
    static const char dashboardTemplate[] PROGMEM;
    static const char switchesTemplate[] PROGMEM;
    static const char wifiTemplate[] PROGMEM;
    static const char wifiConnectTemplate[] PROGMEM;
    static const char wifiHiddenTemplate[] PROGMEM;
    static const char deviceTemplate[] PROGMEM;
    static const char mqttTemplate[] PROGMEM;
    static const char systemUpdateTemplate[] PROGMEM;
    static const char notFoundTemplate[] PROGMEM;

public:
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager = nullptr, TouchSensorManager *touchManager = nullptr, OTAManager *otaManager = nullptr, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _touchManager(touchManager), _otaManager(otaManager), _serverRunning(false), _port(port), _stateChanged(false), _lastStateChange(0)
    {
    }

    // Public methods
    void begin();
    void handleClient();
    bool isRunning();
    void notifyStateChange();
    void stop();

private:
    // Helper function to convert RGB boolean values to color name
    String getColorNameFromRGB(bool r, bool g, bool b)
    {
        if (!r && !g && !b)
            return "off";
        if (r && !g && !b)
            return "red";
        if (!r && g && !b)
            return "green";
        if (!r && !g && b)
            return "blue";
        if (r && g && !b)
            return "yellow";
        if (!r && g && b)
            return "cyan";
        if (r && !g && b)
            return "magenta";
        if (r && g && b)
            return "white";
        return "off";
    }

    // Send complete HTML page using PROGMEM templates
    void sendHtmlPage(const String &content);
    void sendProgmemPage(const char *progmemTemplate);

    // Memory management functions
    void checkMemoryUsage(const String &context = "");
    bool hasEnoughMemory(uint32_t requiredBytes = 1024);
    bool isRequestThrottled(bool isScan = false);

    // Page handlers
    void handleRoot();
    void handleDevice();
    void handleSwitches();
    void handleWiFi();
    void handleWiFiConnectPage();
    void handleWiFiHiddenPage();
    void handleMqtt();
    void handleSystemUpdate();

    // API handlers
    void handleSwitch();
    void handleRGBOff();
    void handleRGBOn();
    void handleWiFiScan();
    void handleWiFiConnect();
    void handleStatusAPI();
    void handleSwitchesAPI();
    void handleSystemUpdateAPI();
    void handleUpdateCheck();
    void handleUpdateToggle();
    void handleInstallUpdate();
    void handle404();
};

// PROGMEM HTML template definitions
const char WebServerManager::htmlHeader[] PROGMEM = R"=====(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-Relay Switch Control</title>
    <script>
    function setRGBColor(switchIndex, state, color) {
        var r = 0, g = 0, b = 0;
        switch(color) {
            case 'red': r = 1; break;
            case 'green': g = 1; break;
            case 'blue': b = 1; break;
            case 'yellow': r = 1; g = 1; break;
            case 'cyan': g = 1; b = 1; break;
            case 'magenta': r = 1; b = 1; break;
            case 'white': r = 1; g = 1; b = 1; break;
            case 'off': break;
        }

        var xhr = new XMLHttpRequest();
        xhr.open('POST', '/rgb/' + state, true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.send('index=' + switchIndex + '&r=' + r + '&g=' + g + '&b=' + b);

        // Update dropdown color class
        var dropdown = event.target;
        if (dropdown) {
            dropdown.className = dropdown.className.replace(/\b(red|green|blue|yellow|cyan|magenta|white|off)\b/g, '');
            dropdown.className += ' ' + color;
        }

        // Update color preview
        var preview = document.getElementById('preview_' + state + '_' + switchIndex);
        if (preview) {
            preview.style.backgroundColor = 'rgb(' + (r*255) + ',' + (g*255) + ',' + (b*255) + ')';
        }
    }
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }

        /* Navigation Bar */
        .navbar {
            background-color: #2d2d2d;
            padding: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            border-bottom: 2px solid #0066cc;
        }
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .nav-brand {
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
            color: #ccc;
            text-decoration: none;
        }
        .nav-toggle {
            display: none;
            background: none;
            border: none;
            color: #e0e0e0;
            font-size: 1.8em;
            padding: 15px 20px;
            cursor: pointer;
            z-index: 1001;
            position: relative;
        }
        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            flex: 1;
        }
        .nav-item {
            margin: 0;
        }
        .nav-link {
            display: block;
            padding: 15px 20px;
            color: #e0e0e0;
            text-decoration: none;
            transition: background-color 0.3s, color 0.3s;
            border-radius: 0;
        }
        .nav-link:hover {
            background-color: #0066cc;
            color: #fff;
        }
        .nav-link.active {
            background-color: #0066cc;
            color: white;
        }

        /* Main Content */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .content-section {
            background-color: #242424;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-top: 3px solid #0066cc;
        }

        h1 {
            color: #ccc;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
        }
        h2 {
            color: #bbb;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h3 {
            color: #bbb;
            margin-bottom: 15px;
        }

        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 30px;
        }
        .info-item {
            background-color: #2d2d2d;
            padding: 12px 16px;
            border-radius: 12px;
            border-left: 4px solid #0066cc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            color: #0066cc;
            font-size: 0.9em;
            flex-shrink: 0;
            margin-right: 10px;
        }
        .info-value {
            color: #e0e0e0;
            font-size: 1.1em;
            word-break: break-word;
            text-align: right;
            flex-grow: 1;
        }

        /* Switch Controls */
        .switch-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .switch-card {
            background-color: #2d2d2d;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #0066cc;
        }
        .switch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .switch-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #bbb;
        }
        .switch-state {
            font-weight: bold;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .switch-state.on {
            background-color: #28a745;
            color: white;
        }
        .switch-state.off {
            background-color: #dc3545;
            color: white;
        }
        .switch-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .switch-status.on {
            background-color: #28a745;
            color: white;
        }
        .switch-status.off {
            background-color: #dc3545;
            color: white;
        }
        .switch-container {
            background-color: #2d2d2d;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }

        /* Toggle Switch */
        .toggle-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .toggle-container span {
            font-weight: bold;
            color: #0066cc;
            font-size: 0.9em;
            flex-shrink: 0;
        }
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background-color: #555;
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .toggle-switch.on {
            background-color: #0066cc;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        .toggle-switch.on::after {
            transform: translateX(30px);
        }

        /* Color Controls */
        .color-controls {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }
        .color-group {
            background-color: #2a2a2a;
            padding: 12px 16px;
            border-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;
        }
        .color-group h4 {
            margin: 0;
            color: #bbb;
            font-size: 0.9em;
            font-weight: bold;
            flex-shrink: 0;
        }
        .color-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .color-dropdown {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #666;
            border-radius: 8px;
            background-color: #2a2a2a;
            color: #e0e0e0;
            font-size: 14px;
            text-align: center;
            max-width: 120px;
        }
        .color-dropdown:focus {
            border-color: #0066cc;
            outline: none;
        }
        .color-dropdown.red { background-color: #cc3333; color: white; }
        .color-dropdown.green { background-color: #33cc33; color: white; }
        .color-dropdown.blue { background-color: #3366cc; color: white; }
        .color-dropdown.yellow { background-color: #cccc33; color: black; }
        .color-dropdown.cyan { background-color: #33cccc; color: black; }
        .color-dropdown.magenta { background-color: #cc33cc; color: white; }
        .color-dropdown.white { background-color: #ffffff; color: black; }
        .color-dropdown.off { background-color: #1a1a1a; color: #888; }

        /* Buttons */
        button, .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #0066cc;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0052a3;
        }
        .btn-secondary {
            background-color: #555;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #666;
        }

        /* Mobile Menu */
        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 999;
        }
        .mobile-overlay.active {
            display: block;
        }

        .mobile-menu {
            display: none;
            position: fixed;
            top: 0;
            right: -300px;
            width: 280px;
            height: 100%;
            background-color: #2d2d2d;
            transition: right 0.3s ease;
            z-index: 1000;
            padding-top: 80px;
        }
        .mobile-menu.active {
            right: 0;
        }
        .mobile-menu .nav-link {
            display: block;
            padding: 20px 25px;
            color: #e0e0e0;
            text-decoration: none;
            border-bottom: 1px solid #444;
            font-size: 1.1em;
        }
        .mobile-menu .nav-link:hover {
            background-color: #0066cc;
        }
        .mobile-menu .nav-link.active {
            background-color: #0066cc;
            color: white;
        }

        /* WiFi Page Styles */
        .wifi-page-container{min-height:calc(100vh - 240px);display:flex;flex-direction:column;}
        .wifi-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;}
        .wifi-title{margin:0;color:#ccc;font-size:2em;}
        .scan-btn{background-color:#0066cc;color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;font-size:14px;transition:background-color 0.3s;}
        .scan-btn:hover{background-color:#0052a3;}
        .scan-btn:disabled{background-color:#666;cursor:not-allowed;}
        .hidden-network-button{background-color:#0066cc;color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;font-size:14px;transition:background-color 0.3s;width:100%;box-sizing:border-box;}
        .hidden-network-button:hover{background-color:#0052a3;}
        .separator-line{height:3px;background-color:#0066cc;margin-bottom:15px;border-radius:2px;}
        .wifi-section{margin-bottom:10px;}
        .wifi-section h2{color:#bbb;border-bottom:2px solid #0066cc;padding-bottom:5px;margin-bottom:10px;}
        .wifi-networks-section{flex:1;display:flex;flex-direction:column;margin-bottom:10px;}
        .wifi-networks-container{flex:1;min-height:200px;max-height:400px;overflow-y:auto;padding-right:5px;}
        .wifi-networks-container::-webkit-scrollbar{width:8px;}
        .wifi-networks-container::-webkit-scrollbar-track{background:#1a1a1a;border-radius:4px;}
        .wifi-networks-container::-webkit-scrollbar-thumb{background:#0066cc;border-radius:4px;}
        .wifi-networks-container::-webkit-scrollbar-thumb:hover{background:#0052a3;}
        .wifi-network{display:flex;justify-content:space-between;align-items:center;padding:20px;margin-bottom:10px;background-color:#2d2d2d;border-radius:15px;border-left:3px solid #0066cc;cursor:pointer;transition:background-color 0.3s,transform 0.2s;}
        .wifi-network:hover{background-color:#3a3a3a;transform:translateY(-2px);}
        .network-info{flex-grow:1;}
        .network-name{font-weight:bold;color:#fff;margin-bottom:5px;font-size:16px;text-align:left;}
        .network-security{font-size:12px;color:#999;text-align:left;}
        .signal-strength{margin-left:15px;text-align:center;}
        .signal-text{font-size:12px;font-weight:bold;padding:4px 8px;border-radius:4px;}
        .signal-excellent{color:#fff;background-color:#4CAF50;}
        .signal-good{color:#fff;background-color:#8BC34A;}
        .signal-fair{color:#fff;background-color:#FF9800;}
        .signal-weak{color:#fff;background-color:#F44336;}
        .loading-message{text-align:center;color:#999;padding:15px;font-style:italic;background-color:#242424;border-radius:15px;}
        .current-status{text-align:center;color:#999;padding:0;font-style:italic;margin:0;}
        .hidden-form{display:none;background-color:#2d2d2d;padding:30px;border-radius:15px;margin-top:20px;border-left:3px solid #0066cc;}
        .form-group{margin-bottom:20px;}
        .form-group label{display:block;color:#ccc;margin-bottom:8px;font-weight:bold;}
        .form-group input{width:100%;padding:12px;border:1px solid #555;border-radius:8px;background-color:#1f1f1f;color:#fff;box-sizing:border-box;font-size:14px;}
        .form-group input:focus{outline:none;border-color:#0066cc;}
        .password-container{position:relative;}
        .password-toggle{position:absolute;right:12px;top:50%;transform:translateY(-50%);background:none;border:none;color:#0066cc;cursor:pointer;font-size:14px;text-align:right;}
        .form-buttons{text-align:center;margin-top:25px;}
        .connect-button,.cancel-button{padding:12px 24px;border:none;border-radius:8px;cursor:pointer;font-size:14px;margin:0 5px;transition:background-color 0.3s;flex:1;}
        .connect-button{background-color:#0066cc;color:white;}
        .connect-button:hover{background-color:#0052a3;}
        .connect-button:disabled{background-color:#666;cursor:not-allowed;}
        .cancel-button{background-color:#666;color:white;}
        .cancel-button:hover{background-color:#555;}
        .button-row{display:flex;gap:10px;width:100%;}
        .loading-spinner{display:inline-block;width:16px;height:16px;border:2px solid #ffffff40;border-top:2px solid #ffffff;border-radius:50%;animation:spin 1s linear infinite;}
        @keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-toggle {
                display: block;
            }
            .nav-menu {
                display: none;
            }
            .mobile-menu {
                display: block;
            }
            .container {
                padding: 10px;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
            .switch-grid {
                grid-template-columns: 1fr;
            }
            .color-controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="nav-brand">3-Relay Switch</a>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/" class="nav-link">Dashboard</a></li>
                <li class="nav-item"><a href="/wifi" class="nav-link">WiFi</a></li>
                <li class="nav-item"><a href="/switches" class="nav-link">Switch Control</a></li>
                <li class="nav-item"><a href="/mqtt" class="nav-link">MQTT</a></li>
                <li class="nav-item"><a href="/system-update" class="nav-link">System Update</a></li>
            </ul>
            <button class="nav-toggle" onclick="toggleMobileMenu()">&equiv;</button>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-overlay" id="mobileOverlay" onclick="closeMobileMenu()"></div>

    <!-- Mobile Side Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <a href="/" class="nav-link">Dashboard</a>
        <a href="/wifi" class="nav-link">WiFi</a>
        <a href="/switches" class="nav-link">Switch Control</a>
        <a href="/mqtt" class="nav-link">MQTT</a>
        <a href="/system-update" class="nav-link">System Update</a>
    </div>
    <div class="container">
)=====";

const char WebServerManager::htmlFooter[] PROGMEM = R"=====(
    </div>
    <script>
        // Set active navigation link
        function setActiveNav(path) {
            var links = document.querySelectorAll('.nav-link');
            links.forEach(function(link) {
                link.classList.remove('active');
                if (link.getAttribute('href') === path) {
                    link.classList.add('active');
                }
            });
        }

        // Toggle mobile menu
        function toggleMobileMenu() {
            var mobileMenu = document.getElementById('mobileMenu');
            var mobileOverlay = document.getElementById('mobileOverlay');
            mobileMenu.classList.toggle('active');
            mobileOverlay.classList.toggle('active');
        }

        // Close mobile menu
        function closeMobileMenu() {
            var mobileMenu = document.getElementById('mobileMenu');
            var mobileOverlay = document.getElementById('mobileOverlay');
            mobileMenu.classList.remove('active');
            mobileOverlay.classList.remove('active');
        }

        // Close mobile menu when clicking on a link
        document.addEventListener('DOMContentLoaded', function() {
            var mobileNavLinks = document.querySelectorAll('.mobile-menu .nav-link');
            mobileNavLinks.forEach(function(link) {
                link.addEventListener('click', function() {
                    closeMobileMenu();
                });
            });
        });

        // Set active nav based on current path
        var currentPath = window.location.pathname;
        setActiveNav(currentPath);
    </script>
</body>
</html>
)=====";

// Dashboard page template
const char WebServerManager::dashboardTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>3-Relay Dashboard</h1>
<h2>WiFi Connection</h2>
<div class='info-grid'>
<div class='info-item'>
<div class='info-label'>Status:</div>
<div class='info-value' id='wifi-status'>Checking...</div>
</div>
<div id='wifi-details'></div>
</div>
<div id='device-info'></div>
</div>
<script>
function loadDashboard(){
console.log('Loading dashboard...');
fetch('/api/status').then(r=>{
console.log('Status API response status:', r.status);
if(!r.ok) throw new Error('Status API failed: ' + r.status);
return r.json();
}).then(d=>{
console.log('Status API data:', d);
document.getElementById('wifi-status').textContent=d.wifi.connected?'Connected':'Disconnected';
let details='';
if(d.wifi.connected){
details+='<div class="info-grid">';
details+='<div class="info-item"><div class="info-label">Network:</div><div class="info-value">'+(d.wifi.ssid||'Unknown')+'</div></div>';
details+='<div class="info-item"><div class="info-label">IP Address:</div><div class="info-value">'+(d.wifi.ip||'Unknown')+'</div></div>';
details+='</div>';
}
document.getElementById('wifi-details').innerHTML=details;
if(d.device){
let deviceHtml='<h2>Device Information</h2><div class="info-grid">';
deviceHtml+='<div class="info-item"><div class="info-label">Device ID:</div><div class="info-value">'+(d.device.id||'Unknown')+'</div></div>';
deviceHtml+='<div class="info-item"><div class="info-label">Device Name:</div><div class="info-value">'+(d.device.name||'Unknown')+'</div></div>';
deviceHtml+='<div class="info-item"><div class="info-label">Device Type:</div><div class="info-value">'+(d.device.type||'Unknown')+'</div></div>';
deviceHtml+='<div class="info-item"><div class="info-label">Switch Count:</div><div class="info-value">'+(d.device.switchCount||'0')+'</div></div>';
deviceHtml+='</div>';
document.getElementById('device-info').innerHTML=deviceHtml;
}
console.log('Dashboard loaded successfully');
}).catch(e=>{
console.error('Dashboard load error:', e);
document.getElementById('wifi-status').textContent='Error loading status';
document.getElementById('device-info').innerHTML='<p style="color: #dc3545;">Error loading device information. Check console for details.</p>';
});
}
window.addEventListener('load',function(){setActiveNav('/');loadDashboard();});
</script>
)=====";

// WiFi connect page template
const char WebServerManager::wifiConnectTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1 class='wifi-title' id='networkTitle' style='margin-bottom: 20px;'>Connect to Network</h1>
<div class='separator-line'></div>
<div class='form-group'>
<label for='networkPassword'>Password:</label>
<div class='password-container'>
<input type='password' id='networkPassword' placeholder='Enter network password'>
<button type='button' id='passwordToggle' class='password-toggle' onclick='togglePasswordVisibility()'>Show</button>
</div>
</div>
<div class='form-buttons' style='margin-top: 25px;'>
<div class='button-row'>
<button onclick='connectToNetwork()' class='connect-button' id='connectBtn'>Connect</button>
<button onclick='goBack()' class='cancel-button'>Cancel</button>
</div>
</div>
</div>
<script>
function getUrlParams(){const params=new URLSearchParams(window.location.search);return{ssid:params.get('ssid'),secure:params.get('secure')==='1'};}
function togglePasswordVisibility(){const i=document.getElementById('networkPassword'),b=document.getElementById('passwordToggle');if(i.type==='password'){i.type='text';b.textContent='Hide';}else{i.type='password';b.textContent='Show';}}
function connectToNetwork(){const params=getUrlParams();const password=document.getElementById('networkPassword').value;const btn=document.getElementById('connectBtn');btn.innerHTML='<span class="loading-spinner"></span> Connecting';btn.disabled=true;fetch('/api/wifi/connect',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'ssid='+encodeURIComponent(params.ssid)+'&password='+encodeURIComponent(password)}).then(r=>r.json()).then(d=>{if(d.success){window.location.href='/wifi';}else{btn.innerHTML='Failed: '+(d.message||'Connection failed');btn.style.backgroundColor='#dc3545';btn.disabled=false;setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);}}).catch(()=>{btn.innerHTML='Connection failed';btn.style.backgroundColor='#dc3545';btn.disabled=false;setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);});}
function goBack(){window.location.href='/wifi';}
window.addEventListener('load',function(){const params=getUrlParams();document.getElementById('networkTitle').textContent='Connect to '+params.ssid;if(!params.secure){document.querySelector('.form-group').style.display='none';}setActiveNav('/wifi');});
</script>
)=====";

// WiFi hidden network page template
const char WebServerManager::wifiHiddenTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1 class='wifi-title' style='margin-bottom: 20px;'>Connect to Hidden Network</h1>
<div class='separator-line'></div>
<div class='form-group'>
<label for='hiddenSSID'>Network Name (SSID):</label>
<input type='text' id='hiddenSSID' placeholder='Enter network name'>
</div>
<div class='form-group'>
<label for='hiddenPassword'>Password:</label>
<div class='password-container'>
<input type='password' id='hiddenPassword' placeholder='Enter password'>
<button type='button' class='password-toggle' onclick='togglePasswordVisibility()'>Show</button>
</div>
</div>
<div class='form-buttons' style='margin-top: 25px;'>
<div class='button-row'>
<button onclick='connectToHiddenNetwork()' class='connect-button' id='connectBtn'>Connect</button>
<button onclick='goBack()' class='cancel-button'>Cancel</button>
</div>
</div>
</div>
<script>
function togglePasswordVisibility(){const i=document.getElementById('hiddenPassword'),b=i.nextElementSibling;if(i.type==='password'){i.type='text';b.textContent='Hide';}else{i.type='password';b.textContent='Show';}}
function connectToHiddenNetwork(){const ssid=document.getElementById('hiddenSSID').value;const password=document.getElementById('hiddenPassword').value;const btn=document.getElementById('connectBtn');if(!ssid){btn.innerHTML='Please enter network name';btn.style.backgroundColor='#dc3545';setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);return;}btn.innerHTML='<span class="loading-spinner"></span> Connecting';btn.disabled=true;fetch('/api/wifi/connect',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'ssid='+encodeURIComponent(ssid)+'&password='+encodeURIComponent(password)}).then(r=>r.json()).then(d=>{if(d.success){window.location.href='/wifi';}else{btn.innerHTML='Failed: '+(d.message||'Connection failed');btn.style.backgroundColor='#dc3545';btn.disabled=false;setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);}}).catch(()=>{btn.innerHTML='Connection failed';btn.style.backgroundColor='#dc3545';btn.disabled=false;setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);});}
function goBack(){window.location.href='/wifi';}
window.addEventListener('load',function(){setActiveNav('/wifi');});
</script>
)=====";

// WiFi page template
const char WebServerManager::wifiTemplate[] PROGMEM = R"=====(
<div class='content-section wifi-page-container'>
<div class='wifi-header'>
<h1 class='wifi-title'>WiFi</h1>
<button onclick='scanWiFi()' id='scanBtn' class='scan-btn'>Scan for Networks</button>
</div>
<div class='separator-line'></div>
<div class='wifi-section' id='currentNetworkSection'>
<h2>Current Network:</h2>
<div id='currentNetwork' class='loading-message'>Checking connection...</div>
</div>
<div class='wifi-networks-section'>
<h2>Available Networks:</h2>
<div class='wifi-networks-container'>
<div id='networkList' class='network-list'>
<div class='loading-message'>Click 'Scan for Networks' to see available WiFi networks</div>
</div>
</div>
</div>
<div style='text-align: center; margin-top: 20px;'>
<button onclick='goToHiddenNetwork()' class='hidden-network-button'>Connect to Hidden Network</button>
</div>
</div>
<script>
function scanWiFi(){const b=document.getElementById('scanBtn'),l=document.getElementById('networkList');b.innerHTML='<span class="loading-spinner"></span> Scanning';b.disabled=1;l.innerHTML='<div class="loading-message"><span class="loading-spinner"></span> Scanning for networks</div>';fetch('/api/wifi/scan').then(r=>r.json()).then(d=>{showNetworks(d.networks);b.innerHTML='Scan for Networks';b.disabled=0;}).catch(()=>{l.innerHTML='<div class="loading-message">Scan failed</div>';b.innerHTML='Scan for Networks';b.disabled=0;});}
function showNetworks(n){const l=document.getElementById('networkList');if(!n||!n.length){l.innerHTML='<div class="loading-message">No networks found</div>';return;}let h='';n.forEach(net=>{const s=getSignal(net.rssi),sec=net.encryption==='none'?'Open':'Secured',pass=net.encryption!=='none';h+='<div class="wifi-network" onclick="goToConnect(\''+net.ssid+'\','+pass+')"><div class="network-info"><div class="network-name">'+net.ssid+'</div><div class="network-security">'+sec+'</div></div><div class="signal-strength"><div class="signal-text '+s.class+'">'+s.text+'</div></div></div>';});l.innerHTML=h;}
function getSignal(r){return r>-50?{class:'signal-excellent',text:'Excellent'}:r>-60?{class:'signal-good',text:'Good'}:r>-70?{class:'signal-fair',text:'Fair'}:{class:'signal-weak',text:'Weak'};}
function goToConnect(ssid,needsPassword){window.location.href='/wifi/connect?ssid='+encodeURIComponent(ssid)+'&secure='+(needsPassword?'1':'0');}
function goToHiddenNetwork(){window.location.href='/wifi/hidden';}
function loadCurrentNetwork(){
console.log('Loading current network status...');
fetch('/api/status').then(r=>{
if(!r.ok) throw new Error('Status API failed: ' + r.status);
return r.json();
}).then(d=>{
console.log('Current network data:', d);
const section=document.getElementById('currentNetworkSection');
const div=document.getElementById('currentNetwork');
if(d&&d.wifi&&d.wifi.connected){
const s=getSignal(d.wifi.rssi||0);
div.innerHTML='<div class="wifi-network"><div class="network-info"><div class="network-name">'+(d.wifi.ssid||'Connected')+'</div><div class="network-security">Connected</div></div><div class="signal-strength"><div class="signal-text '+s.class+'">'+s.text+'</div></div></div>';
div.className='';
section.style.display='block';
}else{
section.style.display='none';
}
}).catch(e=>{
console.error('Failed to load current network:',e);
document.getElementById('currentNetwork').innerHTML='<p style="color: #dc3545;">Error loading network status</p>';
document.getElementById('currentNetworkSection').style.display='block';
});
}
window.addEventListener('load',function(){setActiveNav('/wifi');loadCurrentNetwork();});
</script>
)=====";

// Device control page template
const char WebServerManager::deviceTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>3-Relay Device Control</h1>
<p>Device control functionality will be implemented in a future update.</p>
<div class='button-container'>
<a href='/' class='btn btn-secondary'>← Back to Dashboard</a>
</div>
</div>
<script>window.addEventListener('load', function() { setActiveNav('/device'); });</script>
)=====";

// MQTT status page template
const char WebServerManager::mqttTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>MQTT Status</h1>
<p>MQTT status functionality will be implemented in a future update.</p>
<div class='button-container'>
<a href='/' class='btn btn-secondary'>← Back to Dashboard</a>
</div>
</div>
<script>window.addEventListener('load', function() { setActiveNav('/mqtt'); });</script>
)=====";

// System Update page template
const char WebServerManager::systemUpdateTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>System Update</h1>

<div class='info-card'>
<h3>Current Version</h3>
<p id='currentVersion'>Loading...</p>
</div>

<div class='info-card'>
<h3>Update Status</h3>
<p id='updateStatus'>Click "Check for Updates" to check status</p>
</div>

<div class='info-card'>
<div class='auto-update-header'>
<h3>Auto Update</h3>
<label class='toggle-switch'>
<input type='checkbox' id='autoUpdateToggle'>
<span class='toggle-slider'></span>
</label>
</div>
<p class='help-text'><span class='warning-icon'>⚠️</span> When enabled, updates will be installed automatically when available.</p>
</div>

<div class='button-container'>
<button class='btn btn-primary' id='checkUpdateBtn' onclick='checkForUpdates()'>Check for Updates</button>
<button class='btn btn-success' id='installUpdateBtn' onclick='installUpdate()' style='display:none;'>Install Update</button>
</div>

<div id='updateInfo' class='update-available-card' style='display:none;'>
<h3>Update Available</h3>
<p><strong>New Version:</strong> <span id='newVersion'></span></p>
<p><strong>Release Notes:</strong></p>
<div id='releaseNotes'></div>
</div>

<div id='updateProgress' class='info-card' style='display:none;'>
<h3>Update Progress</h3>
<div class='progress-bar'>
<div class='progress-fill' id='progressFill'></div>
</div>
<p id='progressText'>Preparing update...</p>
</div>
</div>

<style>
.auto-update-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #555;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #0066cc;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #333;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background-color: #0066cc;
    width: 0%;
    transition: width 0.3s ease;
}

.help-text {
    font-size: 0.9em;
    color: #ffd700;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.warning-icon {
    font-size: 1.1em;
}

.update-available-card {
    background-color: #2d2d2d;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 20px;
    margin: 12px 0;
    border-left: 4px solid #0066cc;
}

#updateProgress {
    border-left: 4px solid #28a745;
}
</style>

<script>
let updateAvailable = false;
let autoUpdateEnabled = false;

function loadSystemInfo() {
    fetch('/api/system-update')
        .then(response => response.json())
        .then(data => {
            document.getElementById('currentVersion').textContent = data.currentVersion;
            autoUpdateEnabled = data.autoUpdate;
            document.getElementById('autoUpdateToggle').checked = autoUpdateEnabled;

            if (data.updateStatus) {
                document.getElementById('updateStatus').textContent = data.updateStatus;
            }
        })
        .catch(error => {
            console.error('Error loading system info:', error);
            document.getElementById('currentVersion').textContent = 'Error loading version';
            document.getElementById('updateStatus').textContent = 'Error loading status';
        });
}

function checkForUpdates() {
    const btn = document.getElementById('checkUpdateBtn');
    const status = document.getElementById('updateStatus');

    btn.disabled = true;
    btn.textContent = 'Checking...';
    status.textContent = 'Checking for updates...';

    document.getElementById('updateInfo').style.display = 'none';
    document.getElementById('installUpdateBtn').style.display = 'none';

    fetch('/api/update-check', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            btn.disabled = false;
            btn.textContent = 'Check for Updates';

            if (data.error) {
                status.textContent = data.error;
                status.style.color = '#ff6b6b';
            } else if (data.updateAvailable) {
                updateAvailable = true;
                status.textContent = 'Update available!';
                status.style.color = '#28a745';

                document.getElementById('newVersion').textContent = data.version;
                document.getElementById('releaseNotes').textContent = data.releaseNotes;
                document.getElementById('updateInfo').style.display = 'block';
                document.getElementById('installUpdateBtn').style.display = 'inline-block';
            } else {
                status.textContent = 'No updates available';
                status.style.color = '#28a745';
            }
        })
        .catch(error => {
            console.error('Error checking for updates:', error);
            btn.disabled = false;
            btn.textContent = 'Check for Updates';
            status.textContent = 'Error checking for updates';
            status.style.color = '#ff6b6b';
        });
}

function installUpdate() {
    if (!updateAvailable) return;

    const btn = document.getElementById('installUpdateBtn');
    const progress = document.getElementById('updateProgress');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    btn.disabled = true;
    btn.textContent = 'Installing...';
    progress.style.display = 'block';

    // Simulate progress (actual progress will be handled by the ESP)
    let progressValue = 0;
    const progressInterval = setInterval(() => {
        progressValue += 2;
        progressFill.style.width = progressValue + '%';
        progressText.textContent = `Installing update... ${progressValue}%`;

        if (progressValue >= 100) {
            clearInterval(progressInterval);
            progressText.textContent = 'Update complete! Device will restart...';
        }
    }, 100);

    fetch('/api/install-update', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                progressText.textContent = 'Update installed successfully! Device restarting...';
            } else {
                clearInterval(progressInterval);
                btn.disabled = false;
                btn.textContent = 'Install Update';
                progressText.textContent = 'Update failed: ' + (data.error || 'Unknown error');
                progressText.style.color = '#ff6b6b';
            }
        })
        .catch(error => {
            console.error('Error installing update:', error);
            clearInterval(progressInterval);
            btn.disabled = false;
            btn.textContent = 'Install Update';
            progressText.textContent = 'Update failed: Network error';
            progressText.style.color = '#ff6b6b';
        });
}

document.getElementById('autoUpdateToggle').addEventListener('change', function() {
    const enabled = this.checked;

    fetch('/api/auto-update-toggle', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: enabled })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            autoUpdateEnabled = enabled;
        } else {
            // Revert toggle if failed
            this.checked = !enabled;
            alert('Failed to update auto-update setting');
        }
    })
    .catch(error => {
        console.error('Error updating auto-update setting:', error);
        // Revert toggle if failed
        this.checked = !enabled;
        alert('Failed to update auto-update setting');
    });
});

window.addEventListener('load', function() {
    setActiveNav('/system-update');
    loadSystemInfo();
});
</script>
)=====";

// 404 error page template
const char WebServerManager::notFoundTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Page Not Found</h1>
<p>The requested page could not be found.</p>
<div class='button-container'>
<a href='/' class='btn btn-primary'>← Back to Dashboard</a>
</div>
</div>
)=====";

// Switches page template (loads content dynamically via JavaScript)
const char WebServerManager::switchesTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>3-Relay Switch Control</h1>
<div class='switch-grid' id='switchGrid'>
<div class='loading-message'>Loading switches...</div>
</div>
</div>
<script>
function toggleSwitch(i){fetch('/switch',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'index='+i+'&state='+(document.querySelector('.switch-card:nth-child('+(i+1)+') .switch-state').classList.contains('on')?'0':'1')}).then(()=>setTimeout(updateSwitches,100));}
function updateSwitches(){
fetch('/api/status').then(r=>{
if(!r.ok) throw new Error('Status API failed: ' + r.status);
return r.json();
}).then(d=>{
if(d.switches){
d.switches.forEach((s,i)=>{
const c=document.querySelector('.switch-card:nth-child('+(i+1)+')');
if(c){
const st=c.querySelector('.switch-state');
const sw=c.querySelector('.toggle-switch');
if(st){st.textContent=s.state?'ON':'OFF';st.className='switch-state '+(s.state?'on':'off');}
if(sw)sw.className='toggle-switch '+(s.state?'on':'off');
}
});
}
}).catch(e=>console.error('Switch update error:', e));
}
function loadSwitches(){
console.log('Loading switches...');
fetch('/api/switches').then(r=>{
console.log('Switches API response status:', r.status);
if(!r.ok) throw new Error('Switches API failed: ' + r.status);
return r.text();
}).then(html=>{
console.log('Switches HTML loaded, length:', html.length);
document.getElementById('switchGrid').innerHTML=html;
document.querySelectorAll('.color-dropdown').forEach(sel=>{
sel.value=sel.className.split(' ')[1];
sel.onchange=function(){
const i=this.dataset.switch;const s=this.dataset.state;const c=this.value;
let r=0,g=0,b=0;
if(c==='red')r=1;else if(c==='green')g=1;else if(c==='blue')b=1;else if(c==='yellow'){r=1;g=1;}else if(c==='cyan'){g=1;b=1;}else if(c==='magenta'){r=1;b=1;}else if(c==='white'){r=1;g=1;b=1;}
fetch('/rgb/'+s,{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'index='+i+'&r='+r+'&g='+g+'&b='+b});
this.className='color-dropdown '+c;
};
});
updateSwitches();
setInterval(updateSwitches,500);
console.log('Switches loaded successfully');
}).catch(e=>{
console.error('Switches load error:', e);
document.getElementById('switchGrid').innerHTML='<p style="color: #dc3545;">Error loading switches. Check console for details.</p>';
});
}
window.addEventListener('load',function(){setActiveNav('/switches');loadSwitches();});
</script>
)=====";

// Implementation of WebServerManager methods
void WebServerManager::sendHtmlPage(const String &content)
{
    // Calculate required size more accurately
    size_t headerSize = strlen_P(htmlHeader);
    size_t footerSize = strlen_P(htmlFooter);
    size_t totalSize = headerSize + content.length() + footerSize + 100; // +100 for safety margin

    // Check if we have enough memory for the complete page
    if (ESP.getFreeHeap() < totalSize + 1024) // Need extra 1KB for operations
    {
        Serial.print("Insufficient memory for page - Required: ");
        Serial.print(totalSize);
        Serial.print(", Available: ");
        Serial.println(ESP.getFreeHeap());
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Insufficient Memory");
        return;
    }

    String html;
    html.reserve(totalSize); // Reserve exact space needed

    // Read header and footer from PROGMEM and add dynamic content
    html = FPSTR(htmlHeader);
    html += content;
    html += FPSTR(htmlFooter);

    _server.send(200, "text/html", html);
}

void WebServerManager::sendProgmemPage(const char *progmemTemplate)
{
    // Calculate required size for PROGMEM template
    size_t headerSize = strlen_P(htmlHeader);
    size_t templateSize = strlen_P(progmemTemplate);
    size_t footerSize = strlen_P(htmlFooter);
    size_t totalSize = headerSize + templateSize + footerSize + 100; // +100 for safety margin

    // Check if we have enough memory
    if (ESP.getFreeHeap() < totalSize + 1024) // Need extra 1KB for operations
    {
        Serial.print("Insufficient memory for PROGMEM page - Required: ");
        Serial.print(totalSize);
        Serial.print(", Available: ");
        Serial.println(ESP.getFreeHeap());
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Insufficient Memory");
        return;
    }

    String html;
    html.reserve(totalSize);

    // Build page from PROGMEM templates
    html = FPSTR(htmlHeader);
    html += FPSTR(progmemTemplate);
    html += FPSTR(htmlFooter);

    _server.send(200, "text/html", html);
}

void WebServerManager::begin()
{
    // Main page
    _server.on("/", [this]()
               { handleRoot(); });

    // Device control routes
    _server.on("/device", [this]()
               { handleDevice(); });
    _server.on("/switches", [this]()
               { handleSwitches(); });
    _server.on("/switch", HTTP_GET, [this]()
               { handleSwitch(); });
    _server.on("/switch", HTTP_POST, [this]()
               { handleSwitch(); });
    _server.on("/rgb/off", [this]()
               { handleRGBOff(); });
    _server.on("/rgb/on", [this]()
               { handleRGBOn(); });

    // WiFi and MQTT routes
    _server.on("/wifi", [this]()
               { handleWiFi(); });
    _server.on("/wifi/connect", [this]()
               { handleWiFiConnectPage(); });
    _server.on("/wifi/hidden", [this]()
               { handleWiFiHiddenPage(); });
    _server.on("/mqtt", [this]()
               { handleMqtt(); });
    _server.on("/system-update", [this]()
               { handleSystemUpdate(); });

    // WiFi API endpoints
    _server.on("/api/wifi/scan", [this]()
               { handleWiFiScan(); });
    _server.on("/api/wifi/connect", HTTP_POST, [this]()
               { handleWiFiConnect(); });

    // Status API endpoint for real-time updates
    _server.on("/api/status", [this]()
               { handleStatusAPI(); });

    // Switches API endpoint for dynamic content
    _server.on("/api/switches", [this]()
               { handleSwitchesAPI(); });

    // System Update API endpoints
    _server.on("/api/system-update", [this]()
               { handleSystemUpdateAPI(); });
    _server.on("/api/update-check", HTTP_POST, [this]()
               { handleUpdateCheck(); });
    _server.on("/api/auto-update-toggle", HTTP_POST, [this]()
               { handleUpdateToggle(); });
    _server.on("/api/install-update", HTTP_POST, [this]()
               { handleInstallUpdate(); });

    // 404 handler
    _server.onNotFound([this]()
                       { handle404(); });

    _server.begin();
    _serverRunning = true;

    Serial.println("3-Relay Web server started");
    Serial.print("3-Relay Web server running on port ");
    Serial.println(_port);
}

void WebServerManager::handleClient()
{
    if (_serverRunning)
    {
        // Check memory before handling client
        if (hasEnoughMemory(1024))
        {
            _server.handleClient();
            yield();       // Allow other tasks to run
            ESP.wdtFeed(); // Feed watchdog timer
        }
        else
        {
            Serial.println("Skipping client handling due to low memory");
            delay(100); // Brief delay to allow memory recovery
        }
    }
}

bool WebServerManager::isRunning()
{
    return _serverRunning;
}

void WebServerManager::notifyStateChange()
{
    _stateChanged = true;
    _lastStateChange = millis();
}

void WebServerManager::stop()
{
    _server.stop();
    _serverRunning = false;
    Serial.println("3-Relay Web server stopped");
}

// Memory management functions
void WebServerManager::checkMemoryUsage(const String &context)
{
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 2048) // Less than 2KB free
    {
        Serial.print("WARNING: Low memory in ");
        Serial.print(context);
        Serial.print(" - Free heap: ");
        Serial.println(freeHeap);
    }
    else
    {
        Serial.print("Memory OK in ");
        Serial.print(context);
        Serial.print(" - Free heap: ");
        Serial.println(freeHeap);
    }
}

bool WebServerManager::hasEnoughMemory(uint32_t requiredBytes)
{
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < requiredBytes)
    {
        Serial.print("Insufficient memory - Required: ");
        Serial.print(requiredBytes);
        Serial.print(", Available: ");
        Serial.println(freeHeap);
        return false;
    }
    return true;
}

bool WebServerManager::isRequestThrottled(bool isScan)
{
    unsigned long currentTime = millis();
    String clientIP = _server.client().remoteIP().toString();

    if (isScan)
    {
        if (currentTime - _lastScanRequest < SCAN_THROTTLE_MS)
        {
            Serial.println("Scan request throttled");
            return true;
        }
        _lastScanRequest = currentTime;
    }
    else
    {
        if (currentTime - _lastPageRequest < PAGE_THROTTLE_MS && _currentRequestingIP == clientIP)
        {
            Serial.println("Page request throttled for IP: " + clientIP);
            return true;
        }
        _lastPageRequest = currentTime;
        _currentRequestingIP = clientIP;
    }

    return false;
}

// Handle root page (dashboard)
void WebServerManager::handleRoot()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("Dashboard page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before dashboard: ");
    Serial.println(freeHeap);

    if (freeHeap < 1024)
    {
        Serial.println("Dashboard rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send dashboard using PROGMEM template (no string building!)
    sendProgmemPage(dashboardTemplate);

    Serial.print("Free heap after dashboard: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("Dashboard page sent successfully");
}

// Handle device control page (simplified for 3-Relay)
void WebServerManager::handleDevice()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    Serial.println("Device page requested");

    // Send device page using PROGMEM template (no string building!)
    sendProgmemPage(deviceTemplate);

    Serial.println("Device page sent successfully");
}

// Handle switches control page
void WebServerManager::handleSwitches()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    Serial.println("Switches page requested");

    // Send switches page using PROGMEM template (loads content via AJAX)
    sendProgmemPage(switchesTemplate);

    Serial.println("Switches page sent successfully");
}

// Handle switches API - returns just the switch cards HTML
void WebServerManager::handleSwitchesAPI()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    // Check memory before building HTML response
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 3072) // Need at least 3KB for HTML building
    {
        Serial.print("Switches API rejected due to low memory: ");
        Serial.println(freeHeap);
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    Serial.print("Switches API - Free heap: ");
    Serial.println(freeHeap);

    // Build minimal switch cards HTML
    String html = "";

    for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
    {
        bool state = _deviceManager->getSwitchState(i);
        bool rOff, gOff, bOff, rOn, gOn, bOn;
        _deviceManager->getRGBOff(i, rOff, gOff, bOff);
        _deviceManager->getRGBOn(i, rOn, gOn, bOn);
        String offColor = getColorNameFromRGB(rOff, gOff, bOff);
        String onColor = getColorNameFromRGB(rOn, gOn, bOn);

        html += "<div class='switch-card'>";
        html += "<div class='switch-header'>";
        html += "<div class='switch-title'>Switch " + String(i + 1) + "</div>";
        html += "<span class='switch-state " + String(state ? "on" : "off") + "'>" + String(state ? "ON" : "OFF") + "</span>";
        html += "</div>";
        html += "<div class='toggle-container'>";
        html += "<span>Toggle Switch:</span>";
        html += "<div class='toggle-switch " + String(state ? "on" : "off") + "' onclick='toggleSwitch(" + String(i) + ")'></div>";
        html += "</div>";
        html += "<div class='color-controls'>";
        html += "<div class='color-group'><h4>OFF LED</h4>";
        html += "<select class='color-dropdown " + offColor + "' data-switch='" + String(i) + "' data-state='off'>";
        html += "<option value='off'>Off</option><option value='red'>Red</option><option value='green'>Green</option>";
        html += "<option value='blue'>Blue</option><option value='yellow'>Yellow</option><option value='cyan'>Cyan</option>";
        html += "<option value='magenta'>Magenta</option><option value='white'>White</option>";
        html += "</select></div>";
        html += "<div class='color-group'><h4>ON LED</h4>";
        html += "<select class='color-dropdown " + onColor + "' data-switch='" + String(i) + "' data-state='on'>";
        html += "<option value='off'>Off</option><option value='red'>Red</option><option value='green'>Green</option>";
        html += "<option value='blue'>Blue</option><option value='yellow'>Yellow</option><option value='cyan'>Cyan</option>";
        html += "<option value='magenta'>Magenta</option><option value='white'>White</option>";
        html += "</select></div>";
        html += "</div>";
        html += "</div>";
    }

    _server.send(200, "text/html", html);
}

// Handle WiFi configuration page
void WebServerManager::handleWiFi()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("WiFi page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before WiFi page: ");
    Serial.println(freeHeap);

    if (freeHeap < 1024)
    {
        Serial.println("WiFi page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send WiFi page using PROGMEM template (no string building!)
    sendProgmemPage(wifiTemplate);

    Serial.print("Free heap after WiFi page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("WiFi page sent successfully");
}

// Handle WiFi connect page
void WebServerManager::handleWiFiConnectPage()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("WiFi connect page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before WiFi connect page: ");
    Serial.println(freeHeap);

    if (freeHeap < 1024)
    {
        Serial.println("WiFi connect page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send WiFi connect page using PROGMEM template
    sendProgmemPage(wifiConnectTemplate);

    Serial.print("Free heap after WiFi connect page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("WiFi connect page sent successfully");
}

// Handle WiFi hidden network page
void WebServerManager::handleWiFiHiddenPage()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("WiFi hidden network page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before WiFi hidden page: ");
    Serial.println(freeHeap);

    if (freeHeap < 1024)
    {
        Serial.println("WiFi hidden page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send WiFi hidden page using PROGMEM template
    sendProgmemPage(wifiHiddenTemplate);

    Serial.print("Free heap after WiFi hidden page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("WiFi hidden page sent successfully");
}

// Handle MQTT status page
void WebServerManager::handleMqtt()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("MQTT page requested");

    // Send MQTT page using PROGMEM template (no string building!)
    sendProgmemPage(mqttTemplate);

    Serial.println("MQTT page sent successfully");
}

// Handle switch control
void WebServerManager::handleSwitch()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    if (_server.method() == HTTP_POST)
    {
        if (_server.hasArg("index") && _server.hasArg("state"))
        {
            int index = _server.arg("index").toInt();
            bool state = _server.arg("state").toInt() == 1;

            if (index >= 0 && index < _deviceManager->getSwitchCount())
            {
                _deviceManager->setSwitchState(index, state);
                _server.send(200, "text/plain", "OK");
                return;
            }
        }
    }

    _server.send(400, "text/plain", "Bad Request");
}

// Handle RGB OFF color setting
void WebServerManager::handleRGBOff()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    if (_server.method() == HTTP_POST)
    {
        if (_server.hasArg("index") && _server.hasArg("r") && _server.hasArg("g") && _server.hasArg("b"))
        {
            int index = _server.arg("index").toInt();
            bool r = _server.arg("r").toInt() == 1;
            bool g = _server.arg("g").toInt() == 1;
            bool b = _server.arg("b").toInt() == 1;

            if (index >= 0 && index < _deviceManager->getSwitchCount())
            {
                _deviceManager->setRGBOff(index, r, g, b);
                _server.send(200, "text/plain", "OK");
                return;
            }
        }
    }

    _server.send(400, "text/plain", "Bad Request");
}

// Handle RGB ON color setting
void WebServerManager::handleRGBOn()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    if (_server.method() == HTTP_POST)
    {
        if (_server.hasArg("index") && _server.hasArg("r") && _server.hasArg("g") && _server.hasArg("b"))
        {
            int index = _server.arg("index").toInt();
            bool r = _server.arg("r").toInt() == 1;
            bool g = _server.arg("g").toInt() == 1;
            bool b = _server.arg("b").toInt() == 1;

            if (index >= 0 && index < _deviceManager->getSwitchCount())
            {
                _deviceManager->setRGBOn(index, r, g, b);
                _server.send(200, "text/plain", "OK");
                return;
            }
        }
    }

    _server.send(400, "text/plain", "Bad Request");
}

// Handle WiFi scan API
void WebServerManager::handleWiFiScan()
{
    if (isRequestThrottled(true)) // Use scan throttling
    {
        _server.send(429, "application/json", "{\"error\":\"Too many scan requests\"}");
        return;
    }

    Serial.println("WiFi scan requested");

    // Check memory before scanning
    if (!hasEnoughMemory(2048))
    {
        _server.send(503, "application/json", "{\"error\":\"Insufficient memory for scan\"}");
        return;
    }

    // Perform WiFi scan
    int networkCount = WiFi.scanNetworks();

    String json = "{\"networks\":[";

    if (networkCount > 0)
    {
        for (int i = 0; i < networkCount && i < 20; i++) // Limit to 20 networks
        {
            if (i > 0)
                json += ",";

            json += "{";
            json += "\"ssid\":\"" + WiFi.SSID(i) + "\",";
            json += "\"rssi\":" + String(WiFi.RSSI(i)) + ",";
            json += "\"encryption\":\"" + String(WiFi.encryptionType(i) == ENC_TYPE_NONE ? "none" : "secured") + "\"";
            json += "}";
        }
    }

    json += "]}";

    // Clean up scan results
    WiFi.scanDelete();

    _server.send(200, "application/json", json);
    Serial.println("WiFi scan completed, found " + String(networkCount) + " networks");
}

// Handle WiFi connect API
void WebServerManager::handleWiFiConnect()
{
    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"message\":\"Method not allowed\"}");
        return;
    }

    if (!_server.hasArg("ssid"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"SSID required\"}");
        return;
    }

    String ssid = _server.arg("ssid");
    String password = _server.hasArg("password") ? _server.arg("password") : "";

    Serial.println("WiFi connect request for SSID: " + ssid);

    if (ssid.length() == 0)
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"SSID cannot be empty\"}");
        return;
    }

    // Use WiFiManager if available, otherwise use direct WiFi connection
    bool success = false;
    String message = "";

    if (_wifiManager)
    {
        success = _wifiManager->connect(ssid, password);
        if (success)
        {
            message = "Connected successfully";
            // Save credentials to EEPROM on successful connection
            _wifiManager->saveCredentials(ssid, password);
        }
        else
        {
            message = "Failed to connect to network";
        }
    }
    else
    {
        // Fallback to direct WiFi connection
        WiFi.begin(ssid.c_str(), password.c_str());

        // Wait up to 10 seconds for connection
        int attempts = 0;
        while (WiFi.status() != WL_CONNECTED && attempts < 20)
        {
            delay(500);
            attempts++;
            yield(); // Allow other tasks to run
        }

        success = (WiFi.status() == WL_CONNECTED);
        if (success)
        {
            message = "Connected successfully";
        }
        else
        {
            message = "Failed to connect - check credentials";
        }
    }

    String json = "{\"success\":" + String(success ? "true" : "false") + ",\"message\":\"" + message + "\"}";
    _server.send(200, "application/json", json);

    Serial.println("WiFi connect result: " + String(success ? "SUCCESS" : "FAILED"));
}

// Handle status API for real-time updates
void WebServerManager::handleStatusAPI()
{
    if (!_deviceManager)
    {
        _server.send(404, "application/json", "{\"error\":\"Device manager not initialized\"}");
        return;
    }

    // Check memory before building JSON response
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 2048) // Need at least 2KB for JSON building
    {
        Serial.print("Status API rejected due to low memory: ");
        Serial.println(freeHeap);
        _server.send(503, "application/json", "{\"error\":\"Low memory\"}");
        return;
    }

    Serial.print("Status API - Free heap: ");
    Serial.println(freeHeap);

    String json = "{";

    // WiFi status with detailed information
    json += "\"wifi\":{";
    bool wifiConnected = (_wifiManager && _wifiManager->isConnected()) || (WiFi.status() == WL_CONNECTED);
    json += "\"connected\":" + String(wifiConnected ? "true" : "false");

    if (wifiConnected)
    {
        String ssid = "";
        int32_t rssi = 0;

        if (_wifiManager)
        {
            ssid = _wifiManager->getSSID();
            // Note: RSSI might not be available through WiFiManager, use WiFi.RSSI() as fallback
            rssi = WiFi.RSSI();
        }
        else
        {
            ssid = WiFi.SSID();
            rssi = WiFi.RSSI();
        }

        json += ",\"ssid\":\"" + ssid + "\"";
        json += ",\"rssi\":" + String(rssi);
        json += ",\"ip\":\"" + WiFi.localIP().toString() + "\"";
    }

    json += "},";

    // Device information (constants are now guaranteed valid)
    json += "\"device\":{";

    // Get device information (constants + device name from EEPROM)
    String deviceID = _deviceManager->getDeviceID();
    String deviceName = _deviceManager->getDeviceName();
    String deviceType = _deviceManager->getDeviceType();
    uint8_t switchCount = _deviceManager->getSwitchCount();

    // Only need to check device name since it comes from EEPROM
    if (deviceName.length() == 0)
        deviceName = "Unknown";

    json += "\"id\":\"" + deviceID + "\",";
    json += "\"name\":\"" + deviceName + "\",";
    json += "\"type\":\"" + deviceType + "\",";
    json += "\"switchCount\":" + String(switchCount);
    json += "},";

    // Switch states
    json += "\"switches\":[";
    for (uint8_t i = 0; i < switchCount; i++)
    {
        if (i > 0)
            json += ",";
        json += "{\"state\":";
        json += _deviceManager->getSwitchState(i) ? "true" : "false";
        json += "}";
    }

    json += "],";
    json += "\"stateChanged\":";
    json += _stateChanged ? "true" : "false";
    json += "}";

    // Reset state changed flag after reporting
    _stateChanged = false;

    _server.send(200, "application/json", json);
}

// Handle 404 errors
void WebServerManager::handle404()
{
    Serial.println("404 page requested");

    // Send 404 page using PROGMEM template (no string building!)
    sendProgmemPage(notFoundTemplate);

    Serial.println("404 page sent successfully");
}

// Handle System Update page
void WebServerManager::handleSystemUpdate()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("System Update page requested");

    // Send System Update page using PROGMEM template
    sendProgmemPage(systemUpdateTemplate);

    Serial.println("System Update page sent successfully");
}

// Handle System Update API (get current status)
void WebServerManager::handleSystemUpdateAPI()
{
    if (!_otaManager)
    {
        _server.send(404, "application/json", "{\"error\":\"OTA manager not initialized\"}");
        return;
    }

    // Get current version from constants
    extern const char *CURRENT_VERSION;
    extern const char *DEVICE_MODEL;

    // Check if auto-update is enabled (stored in EEPROM)
    bool autoUpdate = EEPROM.read(500) == 1; // Use address 500 for auto-update setting

    String response = "{";
    response += "\"currentVersion\":\"" + String(CURRENT_VERSION) + "\",";
    response += "\"deviceModel\":\"" + String(DEVICE_MODEL) + "\",";
    response += "\"autoUpdate\":" + String(autoUpdate ? "true" : "false") + ",";
    response += "\"updateStatus\":\"Click 'Check for Updates' to check status\"";
    response += "}";

    _server.send(200, "application/json", response);
}

// Handle update check request
void WebServerManager::handleUpdateCheck()
{
    if (!_otaManager)
    {
        _server.send(404, "application/json", "{\"error\":\"OTA manager not initialized\"}");
        return;
    }

    if (!WiFi.isConnected())
    {
        _server.send(200, "application/json", "{\"error\":\"WiFi not connected\"}");
        return;
    }

    Serial.println("Manual update check requested via web interface");

    // Get update information from OTA manager
    OTAManager::UpdateInfo updateInfo = _otaManager->getUpdateInfo();

    String response = "{";
    if (!updateInfo.error.isEmpty())
    {
        response += "\"error\":\"" + updateInfo.error + "\"";
    }
    else if (updateInfo.available)
    {
        response += "\"updateAvailable\":true,";
        response += "\"version\":\"" + updateInfo.version + "\",";
        response += "\"releaseNotes\":\"" + updateInfo.releaseNotes + "\"";
    }
    else
    {
        response += "\"updateAvailable\":false,";
        response += "\"message\":\"No updates available\"";
    }
    response += "}";

    _server.send(200, "application/json", response);
}

// Handle auto-update toggle
void WebServerManager::handleUpdateToggle()
{
    if (_server.hasArg("plain"))
    {
        String body = _server.arg("plain");

        // Simple JSON parsing for enabled field
        bool enabled = body.indexOf("\"enabled\":true") != -1;

        // Store auto-update setting in EEPROM
        EEPROM.write(500, enabled ? 1 : 0);
        EEPROM.commit();

        Serial.print("Auto-update setting changed to: ");
        Serial.println(enabled ? "enabled" : "disabled");

        _server.send(200, "application/json", "{\"success\":true}");
    }
    else
    {
        _server.send(400, "application/json", "{\"error\":\"Invalid request\"}");
    }
}

// Handle install update request
void WebServerManager::handleInstallUpdate()
{
    if (!_otaManager)
    {
        _server.send(404, "application/json", "{\"error\":\"OTA manager not initialized\"}");
        return;
    }

    if (!WiFi.isConnected())
    {
        _server.send(200, "application/json", "{\"error\":\"WiFi not connected\"}");
        return;
    }

    Serial.println("Manual update installation requested via web interface");

    // First check if update is available
    OTAManager::UpdateInfo updateInfo = _otaManager->getUpdateInfo();

    if (!updateInfo.error.isEmpty())
    {
        _server.send(200, "application/json", "{\"error\":\"" + updateInfo.error + "\"}");
        return;
    }

    if (!updateInfo.available)
    {
        _server.send(200, "application/json", "{\"error\":\"No updates available\"}");
        return;
    }

    // Send immediate response
    _server.send(200, "application/json", "{\"success\":true,\"message\":\"Update started\"}");

    // Small delay to ensure response is sent
    delay(100);

    // Trigger the actual update installation
    _otaManager->checkForUpdates();
}

#endif
