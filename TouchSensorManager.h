#ifndef TOUCH_SENSOR_MANAGER_H
#define TOUCH_SENSOR_MANAGER_H

#include <Arduino.h>

// Hardware-defined touch sensor pins for ESP8266 3-Relay variant
#define TOUCH_PIN_1 4  // t1
#define TOUCH_PIN_2 5  // t2
#define TOUCH_PIN_3 12 // t3

class TouchSensorManager
{
private:
    const uint8_t _touchPins[3] = {TOUCH_PIN_1, TOUCH_PIN_2, TOUCH_PIN_3}; // Hardware-defined pins
    bool _lastTouchState[3];                                               // Last touch state for debouncing
    unsigned long _lastTouchTime[3];                                       // Last touch time for debouncing
    unsigned long _debounceDelay;                                          // Debounce delay in milliseconds
    uint8_t _switchCount;                                                  // Number of active switches

    // Callback function pointer for touch events
    void (*_touchCallback)(uint8_t switchIndex);

public:
    TouchSensorManager(uint8_t switchCount = 3)
        : _switchCount(switchCount), _debounceDelay(50), _touchCallback(nullptr)
    {
        // Initialize state arrays
        for (int i = 0; i < 3; i++)
        {
            _lastTouchState[i] = false;
            _lastTouchTime[i] = 0;
        }

        Serial.println("TouchSensorManager constructor - hardware pins:");
        for (int i = 0; i < 3; i++)
        {
            Serial.print("  Pin ");
            Serial.print(i + 1);
            Serial.print(": ");
            Serial.println(_touchPins[i]);
        }
    }

    // Initialize touch sensors
    void begin()
    {
        // Initialize GPIO pins
        for (int i = 0; i < _switchCount; i++)
        {
            pinMode(_touchPins[i], INPUT_PULLUP);
            Serial.print("3-Relay Touch sensor ");
            Serial.print(i + 1);
            Serial.print(" initialized on pin ");
            Serial.println(_touchPins[i]);
        }

        Serial.println("3-Relay Touch Sensor Manager initialized");
    }

    // Set callback function for touch events
    void setTouchCallback(void (*callback)(uint8_t switchIndex))
    {
        _touchCallback = callback;
    }

    // Get touch pin for a specific switch
    uint8_t getTouchPin(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            return _touchPins[switchIndex];
        }
        return 0;
    }

    // Get debounce delay
    unsigned long getDebounceDelay()
    {
        return _debounceDelay;
    }

    // Read touch sensor state (active LOW)
    bool readTouchSensor(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            return digitalRead(_touchPins[switchIndex]) == LOW;
        }
        return false;
    }

    // Handle touch sensors - call this in main loop
    void handleTouchSensors()
    {
        unsigned long currentTime = millis();

        for (uint8_t i = 0; i < _switchCount; i++)
        {
            bool currentTouch = readTouchSensor(i);

            // Debug: Print touch state changes
            if (currentTouch != _lastTouchState[i])
            {
                Serial.print("Touch sensor ");
                Serial.print(i + 1);
                Serial.print(" (pin ");
                Serial.print(_touchPins[i]);
                Serial.print(") state changed to: ");
                Serial.println(currentTouch ? "PRESSED" : "RELEASED");
            }

            // Check for touch press (transition from not touched to touched)
            if (currentTouch && !_lastTouchState[i] &&
                (currentTime - _lastTouchTime[i] > _debounceDelay))
            {
                _lastTouchTime[i] = currentTime;

                Serial.print("3-Relay Touch sensor ");
                Serial.print(i + 1);
                Serial.print(" (pin ");
                Serial.print(_touchPins[i]);
                Serial.println(") pressed - TRIGGERING CALLBACK");

                // Call callback function if set
                if (_touchCallback)
                {
                    Serial.println("Calling touch callback...");
                    _touchCallback(i);
                }
                else
                {
                    Serial.println("ERROR: No touch callback set!");
                }
            }

            _lastTouchState[i] = currentTouch;
        }
    }

    // Get switch count
    uint8_t getSwitchCount()
    {
        return _switchCount;
    }

    // Set switch count
    void setSwitchCount(uint8_t count)
    {
        if (count <= 3)
        {
            _switchCount = count;
        }
    }

    // Test all touch sensors (for debugging)
    void testTouchSensors()
    {
        Serial.println("Testing 3-Relay touch sensors...");
        for (int i = 0; i < _switchCount; i++)
        {
            bool state = readTouchSensor(i);
            Serial.print("Touch sensor ");
            Serial.print(i + 1);
            Serial.print(" (pin ");
            Serial.print(_touchPins[i]);
            Serial.print("): ");
            Serial.println(state ? "PRESSED" : "NOT PRESSED");
        }
    }
};

#endif // TOUCH_SENSOR_MANAGER_H
