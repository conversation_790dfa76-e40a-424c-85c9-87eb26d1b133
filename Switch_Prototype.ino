#include "WiFiManager.h"
#include "WebServerManager.h"
#include "DeviceManager.h"
#include "MQTTManager.h"
#include "ShiftRegisterManager.h"
#include "TouchSensorManager.h"
#include "OTAManager.h"

// Constants
const char *AP_SSID = "ESP8266_6Relay_Test";
const char *AP_PASSWORD = "12345678";
const int CONNECTION_CHECK_INTERVAL = 5000; // 5 seconds
const int MQTT_CHECK_INTERVAL = 2000;       // 2 seconds

// OTA Server configuration
const char *OTA_SERVER_HOST = "***********"; // Change to your laptop's IP
const int OTA_SERVER_PORT = 8080;
const char *UPDATE_CHECK_ENDPOINT = "/check-update";
const char *FIRMWARE_DOWNLOAD_ENDPOINT = "/download-firmware";
const char *DEVICE_MODEL = "ESP8266_Switch";
const char *CURRENT_VERSION = "1.0.0";
const unsigned long UPDATE_CHECK_INTERVAL = 12 * 60 * 60 * 1000; // 12 hours

// Global variables
WiFiManager wifiManager;
DeviceManager deviceManager(3);     // Initialize with 3 switches
TouchSensorManager touchManager(3); // Initialize with 3 touch sensors
OTAManager otaManager(OTA_SERVER_HOST, OTA_SERVER_PORT, UPDATE_CHECK_ENDPOINT, FIRMWARE_DOWNLOAD_ENDPOINT, DEVICE_MODEL, CURRENT_VERSION);
WebServerManager webServerManager(&wifiManager, &deviceManager, &touchManager, &otaManager);
MQTTManager mqttManager(&deviceManager);
unsigned long lastConnectionCheck = 0;
unsigned long lastMqttCheck = 0;
unsigned long lastUpdateCheck = 0;
bool setupMode = false;

// MQTT callback function
void mqttCallback(char *topic, byte *payload, unsigned int length)
{
    mqttManager.handleMessage(topic, payload, length);
}

// Touch sensor callback function
void touchCallback(uint8_t switchIndex)
{
    Serial.println("=== TOUCH CALLBACK TRIGGERED ===");
    Serial.print("Switch index: ");
    Serial.println(switchIndex);

    deviceManager.toggleSwitch(switchIndex);
    Serial.print("Touch triggered switch toggle for switch ");
    Serial.println(switchIndex + 1);

    Serial.println("=== TOUCH CALLBACK COMPLETED ===");
}

// State change callback function for webserver notifications
void stateChangeCallback()
{
    Serial.println("State change detected - notifying webserver");
    webServerManager.notifyStateChange();
    // Brief yield to allow webserver to process the notification
    yield();
}

void setup()
{
    // Initialize serial communication
    Serial.begin(115200);
    delay(1000);
    Serial.println("\n\nESP8266 3-Relay Switch Setup");

    // Initialize EEPROM for all managers
    EEPROM.begin(512);

    // Initialize device manager
    deviceManager.begin();

    // Set up state change callback for webserver notifications
    deviceManager.setStateChangeCallback(stateChangeCallback);
    Serial.println("Device manager state change callback set");

    // Initialize touch sensor manager
    Serial.println("Initializing touch sensor manager...");
    touchManager.begin();
    touchManager.setTouchCallback(touchCallback);
    Serial.println("Touch sensor manager initialized and callback set");

    // Test touch sensors immediately after initialization
    Serial.println("Testing touch sensors after initialization:");
    touchManager.testTouchSensors();

    // Always start the access point and web server
    WiFi.mode(WIFI_AP_STA);
    WiFi.softAP(AP_SSID, AP_PASSWORD);

    Serial.print("Access Point started with SSID: ");
    Serial.println(AP_SSID);
    Serial.print("IP address: ");
    Serial.println(WiFi.softAPIP());

    // Start web server
    webServerManager.begin();

    // Check for stored credentials but don't try to connect immediately
    if (wifiManager.hasStoredCredentials())
    {
        Serial.println("Found stored WiFi credentials");
        setupMode = false; // Will try to connect in main loop

        // Initialize MQTT manager
        mqttManager.begin();
        mqttManager.setCallback(mqttCallback);
    }
    else
    {
        Serial.println("No stored WiFi credentials found");
        setupMode = true;
    }

    // Initialize OTA manager
    otaManager.begin();
    Serial.println("OTA manager initialized");
}

void loop()
{
    // Handle web server clients with memory check
    webServerManager.handleClient();

    unsigned long currentMillis = millis();

    // Handle touch sensors
    touchManager.handleTouchSensors();

    // Feed watchdog timer regularly
    ESP.wdtFeed();

    // Memory monitoring every 30 seconds
    static unsigned long lastMemoryCheck = 0;
    if (currentMillis - lastMemoryCheck >= 30000)
    {
        lastMemoryCheck = currentMillis;
        uint32_t freeHeap = ESP.getFreeHeap();
        Serial.print("Free heap: ");
        Serial.print(freeHeap);
        Serial.println(" bytes");

        if (freeHeap < 3000)
        {
            Serial.println("WARNING: Low memory detected!");
        }
        yield();
    }

    // Check WiFi connection status every 15 seconds (further increased to reduce load)
    if (currentMillis - lastConnectionCheck >= (CONNECTION_CHECK_INTERVAL * 3))
    {
        lastConnectionCheck = currentMillis;
        yield(); // Allow other tasks to run before WiFi operations

        // Only try WiFi connection if we're not in setup mode and don't have a connection
        if (!setupMode && !wifiManager.isConnected())
        {
            // Try non-blocking connection attempt
            static bool connectionInProgress = false;
            static unsigned long connectionStartTime = 0;

            if (!connectionInProgress)
            {
                Serial.println("=== WiFi Connection Debug ===");
                Serial.println("Starting non-blocking WiFi connection attempt...");
                if (wifiManager.hasStoredCredentials())
                {
                    wifiManager.loadCredentials();
                    Serial.print("Connecting to SSID: ");
                    Serial.println(wifiManager.getSSID());
                    Serial.println("Setting WiFi mode to AP+STA...");
                    WiFi.mode(WIFI_AP_STA);
                    WiFi.begin(wifiManager.getSSID().c_str(), wifiManager.getPassword().c_str());
                    connectionInProgress = true;
                    connectionStartTime = currentMillis;
                    Serial.println("WiFi connection initiated, waiting for result...");
                }
                else
                {
                    Serial.println("No stored credentials found, entering setup mode");
                    enterSetupMode();
                }
            }
            else
            {
                // Check connection status
                wl_status_t wifiStatus = WiFi.status();
                if (wifiStatus == WL_CONNECTED)
                {
                    Serial.println("=== WiFi Connection Successful! ===");
                    Serial.print("Connected to: ");
                    Serial.println(WiFi.SSID());
                    Serial.print("IP Address: ");
                    Serial.println(WiFi.localIP());
                    Serial.print("Signal Strength: ");
                    Serial.print(WiFi.RSSI());
                    Serial.println(" dBm");
                    connectionInProgress = false;
                    setupMode = false; // Exit setup mode on successful connection

                    Serial.println("Connecting to MQTT...");
                    mqttManager.connect();

                    Serial.println("=== Starting OTA Update Check ===");
                    Serial.println("WiFi is connected, checking for firmware updates...");
                    otaManager.checkForUpdates();
                    Serial.println("=== OTA Update Check Complete ===");
                }
                else if (currentMillis - connectionStartTime > 20000) // Increased timeout to 20 seconds
                {
                    Serial.print("WiFi connection timeout after 20 seconds. Status: ");
                    Serial.println(wifiStatus);
                    Serial.println("Entering setup mode");
                    connectionInProgress = false;
                    enterSetupMode();
                }
                else
                {
                    // Show connection progress every 5 seconds
                    static unsigned long lastStatusUpdate = 0;
                    if (currentMillis - lastStatusUpdate >= 5000)
                    {
                        lastStatusUpdate = currentMillis;
                        Serial.print("WiFi connecting... Status: ");
                        Serial.print(wifiStatus);
                        Serial.print(" (");
                        Serial.print((currentMillis - connectionStartTime) / 1000);
                        Serial.println("s elapsed)");
                    }
                }
            }
        }
        else if (WiFi.status() == WL_CONNECTED && !setupMode)
        {
            // If we're connected but MQTT isn't, try to connect MQTT
            if (!mqttManager.isConnected())
            {
                Serial.println("WiFi connected but MQTT disconnected, attempting MQTT reconnection...");
                mqttManager.connect();
            }
        }

        // Ensure AP+STA mode is maintained
        if (WiFi.getMode() != WIFI_AP_STA)
        {
            WiFi.mode(WIFI_AP_STA);
            WiFi.softAP(AP_SSID, AP_PASSWORD);
        }
    }

    // Handle MQTT every 2 seconds (reduced for better responsiveness)
    if (currentMillis - lastMqttCheck >= MQTT_CHECK_INTERVAL)
    {
        lastMqttCheck = currentMillis;

        if (wifiManager.isConnected())
        {
            mqttManager.loop();
            yield(); // Allow other tasks to run after MQTT operations
        }
    }

    // Check for OTA updates every 12 hours (only if WiFi is connected)
    if (wifiManager.isConnected() && currentMillis - lastUpdateCheck >= UPDATE_CHECK_INTERVAL)
    {
        lastUpdateCheck = currentMillis;
        Serial.println("=== Scheduled OTA Update Check ===");
        Serial.print("WiFi Status: Connected to ");
        Serial.println(WiFi.SSID());
        Serial.print("Signal Strength: ");
        Serial.print(WiFi.RSSI());
        Serial.println(" dBm");
        Serial.println("Performing scheduled OTA update check...");
        otaManager.checkForUpdates();
        Serial.println("=== Scheduled OTA Check Complete ===");
        yield(); // Allow other tasks to run after OTA operations
    }

    // Small delay to prevent overwhelming the ESP
    delay(1); // Reduced delay for better responsiveness
    yield();  // Ensure other tasks can run
}

// Enter WiFi setup mode
void enterSetupMode()
{
    Serial.println("Entering WiFi setup mode");
    setupMode = true;

    // Make sure access point is running
    if (WiFi.getMode() != WIFI_AP_STA)
    {
        WiFi.mode(WIFI_AP_STA);
        WiFi.softAP(AP_SSID, AP_PASSWORD);

        Serial.print("Access Point started with SSID: ");
        Serial.println(AP_SSID);
        Serial.print("IP address: ");
        Serial.println(WiFi.softAPIP());

        // Start web server if not already running
        if (!webServerManager.isRunning())
        {
            webServerManager.begin();
        }
    }
}
