#include "WiFiManager.h"
#include "WebServerManager.h"
#include "DeviceManager.h"
#include "MQTTManager.h"
#include "ShiftRegisterManager.h"
#include "TouchSensorManager.h"

// Constants
const char *AP_SSID = "ESP8266_6Relay_Test";
const char *AP_PASSWORD = "12345678";
const int CONNECTION_CHECK_INTERVAL = 5000; // 5 seconds
const int MQTT_CHECK_INTERVAL = 2000;       // 2 seconds

// Global variables
WiFiManager wifiManager;
DeviceManager deviceManager(3);     // Initialize with 3 switches
TouchSensorManager touchManager(3); // Initialize with 3 touch sensors
WebServerManager webServerManager(&wifiManager, &deviceManager, &touchManager);
MQTTManager mqttManager(&deviceManager);
unsigned long lastConnectionCheck = 0;
unsigned long lastMqttCheck = 0;
bool setupMode = false;

// MQTT callback function
void mqttCallback(char *topic, byte *payload, unsigned int length)
{
    mqttManager.handleMessage(topic, payload, length);
}

// Touch sensor callback function
void touchCallback(uint8_t switchIndex)
{
    Serial.println("=== TOUCH CALLBACK TRIGGERED ===");
    Serial.print("Switch index: ");
    Serial.println(switchIndex);

    deviceManager.toggleSwitch(switchIndex);
    Serial.print("Touch triggered switch toggle for switch ");
    Serial.println(switchIndex + 1);

    Serial.println("=== TOUCH CALLBACK COMPLETED ===");
}

// State change callback function for webserver notifications
void stateChangeCallback()
{
    Serial.println("State change detected - notifying webserver");
    webServerManager.notifyStateChange();
    // Brief yield to allow webserver to process the notification
    yield();
}

void setup()
{
    // Initialize serial communication
    Serial.begin(115200);
    delay(1000);
    Serial.println("\n\nESP8266 3-Relay Switch Setup");

    // Initialize EEPROM for all managers
    EEPROM.begin(512);

    // Initialize device manager
    deviceManager.begin();

    // Set up state change callback for webserver notifications
    deviceManager.setStateChangeCallback(stateChangeCallback);
    Serial.println("Device manager state change callback set");

    // Initialize touch sensor manager
    Serial.println("Initializing touch sensor manager...");
    touchManager.begin();
    touchManager.setTouchCallback(touchCallback);
    Serial.println("Touch sensor manager initialized and callback set");

    // Test touch sensors immediately after initialization
    Serial.println("Testing touch sensors after initialization:");
    touchManager.testTouchSensors();

    // Always start the access point and web server
    WiFi.mode(WIFI_AP_STA);
    WiFi.softAP(AP_SSID, AP_PASSWORD);

    Serial.print("Access Point started with SSID: ");
    Serial.println(AP_SSID);
    Serial.print("IP address: ");
    Serial.println(WiFi.softAPIP());

    // Start web server
    webServerManager.begin();

    // Check for stored credentials but don't try to connect immediately
    if (wifiManager.hasStoredCredentials())
    {
        Serial.println("Found stored WiFi credentials");
        setupMode = false; // Will try to connect in main loop

        // Initialize MQTT manager
        mqttManager.begin();
        mqttManager.setCallback(mqttCallback);
    }
    else
    {
        Serial.println("No stored WiFi credentials found");
        setupMode = true;
    }
}

void loop()
{
    // Handle web server clients with memory check
    webServerManager.handleClient();

    unsigned long currentMillis = millis();

    // Handle touch sensors
    touchManager.handleTouchSensors();

    // Feed watchdog timer regularly
    ESP.wdtFeed();

    // Memory monitoring every 30 seconds
    static unsigned long lastMemoryCheck = 0;
    if (currentMillis - lastMemoryCheck >= 30000)
    {
        lastMemoryCheck = currentMillis;
        uint32_t freeHeap = ESP.getFreeHeap();
        Serial.print("Free heap: ");
        Serial.print(freeHeap);
        Serial.println(" bytes");

        if (freeHeap < 3000)
        {
            Serial.println("WARNING: Low memory detected!");
        }
        yield();
    }

    // Debug: Test touch sensors every 10 seconds (reduced frequency)
    static unsigned long lastTouchTest = 0;
    if (currentMillis - lastTouchTest >= 10000)
    {
        lastTouchTest = currentMillis;
        Serial.println("=== Touch Sensor Debug ===");
        touchManager.testTouchSensors();
        Serial.println("=========================");
        yield(); // Allow other tasks to run after debug output
    }

    // Check WiFi connection status every 15 seconds (further increased to reduce load)
    if (currentMillis - lastConnectionCheck >= (CONNECTION_CHECK_INTERVAL * 3))
    {
        lastConnectionCheck = currentMillis;
        yield(); // Allow other tasks to run before WiFi operations

        // Only try WiFi connection if we're not in setup mode and don't have a connection
        if (!setupMode && !wifiManager.isConnected())
        {
            // Try non-blocking connection attempt
            static bool connectionInProgress = false;
            static unsigned long connectionStartTime = 0;

            if (!connectionInProgress)
            {
                Serial.println("Starting non-blocking WiFi connection attempt...");
                if (wifiManager.hasStoredCredentials())
                {
                    wifiManager.loadCredentials();
                    WiFi.mode(WIFI_AP_STA);
                    WiFi.begin(wifiManager.getSSID().c_str(), wifiManager.getPassword().c_str());
                    connectionInProgress = true;
                    connectionStartTime = currentMillis;
                }
                else
                {
                    enterSetupMode();
                }
            }
            else
            {
                // Check connection status
                if (WiFi.status() == WL_CONNECTED)
                {
                    Serial.println("WiFi connection successful!");
                    connectionInProgress = false;
                    setupMode = false; // Exit setup mode on successful connection
                    mqttManager.connect();
                }
                else if (currentMillis - connectionStartTime > 20000) // Increased timeout to 20 seconds
                {
                    Serial.println("WiFi connection timeout, entering setup mode");
                    connectionInProgress = false;
                    enterSetupMode();
                }
            }
        }
        else if (WiFi.status() == WL_CONNECTED && !setupMode)
        {
            // If we're connected but MQTT isn't, try to connect MQTT
            if (!mqttManager.isConnected())
            {
                mqttManager.connect();
            }
        }

        // Ensure AP+STA mode is maintained
        if (WiFi.getMode() != WIFI_AP_STA)
        {
            WiFi.mode(WIFI_AP_STA);
            WiFi.softAP(AP_SSID, AP_PASSWORD);
        }
    }

    // Handle MQTT every 2 seconds (reduced for better responsiveness)
    if (currentMillis - lastMqttCheck >= MQTT_CHECK_INTERVAL)
    {
        lastMqttCheck = currentMillis;

        if (wifiManager.isConnected())
        {
            mqttManager.loop();
            yield(); // Allow other tasks to run after MQTT operations
        }
    }

    // Small delay to prevent overwhelming the ESP
    delay(1); // Reduced delay for better responsiveness
    yield();  // Ensure other tasks can run
}

// Enter WiFi setup mode
void enterSetupMode()
{
    Serial.println("Entering WiFi setup mode");
    setupMode = true;

    // Make sure access point is running
    if (WiFi.getMode() != WIFI_AP_STA)
    {
        WiFi.mode(WIFI_AP_STA);
        WiFi.softAP(AP_SSID, AP_PASSWORD);

        Serial.print("Access Point started with SSID: ");
        Serial.println(AP_SSID);
        Serial.print("IP address: ");
        Serial.println(WiFi.softAPIP());

        // Start web server if not already running
        if (!webServerManager.isRunning())
        {
            webServerManager.begin();
        }
    }
}
