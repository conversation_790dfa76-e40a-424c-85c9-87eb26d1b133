#ifndef OTA_MANAGER_H
#define OTA_MANAGER_H

#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#include <ESP8266httpUpdate.h>
#include <ArduinoJson.h>
#include <WiFiClient.h>

class OTAManager
{
private:
    String _serverHost;
    int _serverPort;
    String _updateCheckEndpoint;
    String _firmwareDownloadEndpoint;
    String _deviceModel;
    String _currentVersion;

    WiFiClient _wifiClient;
    HTTPClient _httpClient;

public:
    OTAManager(const char *serverHost, int serverPort, const char *updateCheckEndpoint,
               const char *firmwareDownloadEndpoint, const char *deviceModel, const char *currentVersion)
        : _serverHost(serverHost), _serverPort(serverPort), _updateCheckEndpoint(updateCheckEndpoint),
          _firmwareDownloadEndpoint(firmwareDownloadEndpoint), _deviceModel(deviceModel), _currentVersion(currentVersion)
    {
    }

    void begin()
    {
        Serial.println("OTA Manager initialized");
        Serial.print("Device Model: ");
        Serial.println(_deviceModel);
        Serial.print("Current Version: ");
        Serial.println(_currentVersion);
        Serial.print("OTA Server: ");
        Serial.print(_serverHost);
        Serial.print(":");
        Serial.println(_serverPort);
    }

    void checkForUpdates()
    {
        if (WiFi.status() != WL_CONNECTED)
        {
            Serial.println("=== OTA ERROR ===");
            Serial.println("Cannot check for updates - WiFi not connected");
            Serial.print("WiFi Status: ");
            Serial.println(WiFi.status());
            Serial.println("================");
            return;
        }

        Serial.println("=== OTA Update Check Started ===");
        Serial.print("WiFi Status: Connected to ");
        Serial.println(WiFi.SSID());
        Serial.print("Local IP: ");
        Serial.println(WiFi.localIP());
        Serial.print("Free Heap: ");
        Serial.print(ESP.getFreeHeap());
        Serial.println(" bytes");

        // Prepare JSON payload
        DynamicJsonDocument requestDoc(256);
        requestDoc["model"] = _deviceModel;
        requestDoc["version"] = _currentVersion;

        String requestBody;
        serializeJson(requestDoc, requestBody);

        // Make HTTP POST request
        String url = "http://" + _serverHost + ":" + String(_serverPort) + _updateCheckEndpoint;

        Serial.println("=== OTA Request Details ===");
        Serial.println("Server: " + _serverHost + ":" + String(_serverPort));
        Serial.println("URL: " + url);
        Serial.println("Device Model: " + _deviceModel);
        Serial.println("Current Version: " + _currentVersion);
        Serial.println("Request Body: " + requestBody);

        Serial.println("Initializing HTTP client...");
        _httpClient.begin(_wifiClient, url);
        _httpClient.addHeader("Content-Type", "application/json");
        _httpClient.setTimeout(10000); // 10 second timeout

        Serial.println("Sending POST request to server...");
        unsigned long requestStart = millis();
        int httpResponseCode = _httpClient.POST(requestBody);
        unsigned long requestTime = millis() - requestStart;

        Serial.println("=== OTA Response ===");
        Serial.print("HTTP Response Code: ");
        Serial.println(httpResponseCode);
        Serial.print("Request Time: ");
        Serial.print(requestTime);
        Serial.println(" ms");

        if (httpResponseCode > 0)
        {
            String response = _httpClient.getString();
            Serial.print("Response Length: ");
            Serial.print(response.length());
            Serial.println(" bytes");
            Serial.println("Server Response: " + response);

            // Parse response
            DynamicJsonDocument responseDoc(512);
            DeserializationError error = deserializeJson(responseDoc, response);

            if (!error)
            {
                bool updateAvailable = responseDoc["updateAvailable"];
                Serial.print("Update Available: ");
                Serial.println(updateAvailable ? "YES" : "NO");

                if (updateAvailable)
                {
                    String newVersion = responseDoc["version"];
                    String releaseNotes = responseDoc["releaseNotes"];

                    Serial.println("=== UPDATE FOUND ===");
                    Serial.println("Current Version: " + _currentVersion);
                    Serial.println("New Version: " + newVersion);
                    Serial.println("Release Notes: " + releaseNotes);
                    Serial.println("==================");

                    // Perform OTA update
                    performOTAUpdate();
                }
                else
                {
                    Serial.println("=== NO UPDATES ===");
                    Serial.println("Device is up to date");
                    Serial.println("=================");
                }
            }
            else
            {
                Serial.println("=== JSON PARSE ERROR ===");
                Serial.print("Error: ");
                Serial.println(error.c_str());
                Serial.println("Raw response: " + response);
                Serial.println("=======================");
            }
        }
        else
        {
            Serial.println("=== HTTP REQUEST FAILED ===");
            Serial.print("Error Code: ");
            Serial.println(httpResponseCode);
            if (httpResponseCode == -1)
            {
                Serial.println("Connection failed - check server IP and port");
            }
            else if (httpResponseCode == -11)
            {
                Serial.println("Read timeout - server may be slow or unreachable");
            }
            Serial.println("==========================");
        }

        _httpClient.end();
        Serial.println("=== OTA Update Check Finished ===");
    }

private:
    void performOTAUpdate()
    {
        Serial.println("Starting OTA update...");

        String firmwareUrl = "http://" + _serverHost + ":" + String(_serverPort) + _firmwareDownloadEndpoint;

        // Configure update client with optimizations
        ESPhttpUpdate.setLedPin(LED_BUILTIN, LOW);

        // Set redirect handling for faster operation
        ESPhttpUpdate.setFollowRedirects(HTTPC_STRICT_FOLLOW_REDIRECTS);

        ESPhttpUpdate.onStart([]()
                              {
            Serial.println("OTA update started");
            Serial.println("This should take 30-60 seconds..."); });

        ESPhttpUpdate.onEnd([]()
                            { Serial.println("OTA update finished successfully!"); });

        ESPhttpUpdate.onProgress([](int cur, int total)
                                 {
            static unsigned long lastProgressTime = 0;
            unsigned long now = millis();

            // Update progress every 2 seconds to avoid spam
            if (now - lastProgressTime > 2000 || cur == total) {
                int percentage = (cur * 100) / total;
                Serial.printf("OTA Progress: %u%% (%d/%d bytes)\n", percentage, cur, total);
                lastProgressTime = now;
            } });

        ESPhttpUpdate.onError([](int error)
                              { Serial.printf("OTA Error[%u]: %s\n", error, ESPhttpUpdate.getLastErrorString().c_str()); });

        // Use optimized WiFi client
        WiFiClient updateClient;

        // Perform the update
        Serial.println("Downloading firmware...");
        unsigned long startTime = millis();
        t_httpUpdate_return result = ESPhttpUpdate.update(updateClient, firmwareUrl);
        unsigned long updateTime = millis() - startTime;

        switch (result)
        {
        case HTTP_UPDATE_FAILED:
            Serial.printf("OTA Update failed after %lu ms. Error (%d): %s\n",
                          updateTime, ESPhttpUpdate.getLastError(), ESPhttpUpdate.getLastErrorString().c_str());
            break;

        case HTTP_UPDATE_NO_UPDATES:
            Serial.printf("OTA: No updates available (checked in %lu ms)\n", updateTime);
            break;

        case HTTP_UPDATE_OK:
            Serial.printf("OTA Update successful in %lu ms (%.1f seconds)! Restarting...\n",
                          updateTime, updateTime / 1000.0);
            delay(1000); // Brief delay before restart
            ESP.restart();
            break;
        }
    }
};

#endif // OTA_MANAGER_H
