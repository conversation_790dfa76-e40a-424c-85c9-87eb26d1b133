#ifndef OTA_MANAGER_H
#define OTA_MANAGER_H

#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#include <ESP8266httpUpdate.h>
#include <ArduinoJson.h>
#include <WiFiClient.h>

class OTAManager
{
private:
    String _serverHost;
    int _serverPort;
    String _updateCheckEndpoint;
    String _firmwareDownloadEndpoint;
    String _deviceModel;
    String _currentVersion;
    
    WiFiClient _wifiClient;
    HTTPClient _httpClient;

public:
    OTAManager(const char* serverHost, int serverPort, const char* updateCheckEndpoint, 
               const char* firmwareDownloadEndpoint, const char* deviceModel, const char* currentVersion)
        : _serverHost(serverHost), _serverPort(serverPort), _updateCheckEndpoint(updateCheckEndpoint),
          _firmwareDownloadEndpoint(firmwareDownloadEndpoint), _deviceModel(deviceModel), _currentVersion(currentVersion)
    {
    }

    void begin()
    {
        Serial.println("OTA Manager initialized");
        Serial.print("Device Model: ");
        Serial.println(_deviceModel);
        Serial.print("Current Version: ");
        Serial.println(_currentVersion);
        Serial.print("OTA Server: ");
        Serial.print(_serverHost);
        Serial.print(":");
        Serial.println(_serverPort);
    }

    void checkForUpdates()
    {
        if (WiFi.status() != WL_CONNECTED)
        {
            Serial.println("Cannot check for updates - WiFi not connected");
            return;
        }

        Serial.println("Checking for OTA updates...");

        // Prepare JSON payload
        DynamicJsonDocument requestDoc(256);
        requestDoc["model"] = _deviceModel;
        requestDoc["version"] = _currentVersion;

        String requestBody;
        serializeJson(requestDoc, requestBody);

        // Make HTTP POST request
        String url = "http://" + _serverHost + ":" + String(_serverPort) + _updateCheckEndpoint;

        Serial.println("Request URL: " + url);
        Serial.println("Request body: " + requestBody);

        _httpClient.begin(_wifiClient, url);
        _httpClient.addHeader("Content-Type", "application/json");

        Serial.println("Sending POST request...");
        int httpResponseCode = _httpClient.POST(requestBody);
        Serial.println("HTTP Response Code: " + String(httpResponseCode));

        if (httpResponseCode > 0)
        {
            String response = _httpClient.getString();
            Serial.println("Server response: " + response);

            // Parse response
            DynamicJsonDocument responseDoc(512);
            DeserializationError error = deserializeJson(responseDoc, response);

            if (!error)
            {
                bool updateAvailable = responseDoc["updateAvailable"];

                if (updateAvailable)
                {
                    String newVersion = responseDoc["version"];
                    String releaseNotes = responseDoc["releaseNotes"];

                    Serial.println("Update available!");
                    Serial.println("New version: " + newVersion);
                    Serial.println("Release notes: " + releaseNotes);

                    // Perform OTA update
                    performOTAUpdate();
                }
                else
                {
                    Serial.println("No updates available");
                }
            }
            else
            {
                Serial.println("Failed to parse server response");
            }
        }
        else
        {
            Serial.println("HTTP request failed with code: " + String(httpResponseCode));
        }

        _httpClient.end();
    }

private:
    void performOTAUpdate()
    {
        Serial.println("Starting OTA update...");

        String firmwareUrl = "http://" + _serverHost + ":" + String(_serverPort) + _firmwareDownloadEndpoint;

        // Configure update client with optimizations
        ESPhttpUpdate.setLedPin(LED_BUILTIN, LOW);

        // Set redirect handling for faster operation
        ESPhttpUpdate.setFollowRedirects(HTTPC_STRICT_FOLLOW_REDIRECTS);

        ESPhttpUpdate.onStart([]() {
            Serial.println("OTA update started");
            Serial.println("This should take 30-60 seconds...");
        });

        ESPhttpUpdate.onEnd([]() {
            Serial.println("OTA update finished successfully!");
        });

        ESPhttpUpdate.onProgress([](int cur, int total) {
            static unsigned long lastProgressTime = 0;
            unsigned long now = millis();

            // Update progress every 2 seconds to avoid spam
            if (now - lastProgressTime > 2000 || cur == total) {
                int percentage = (cur * 100) / total;
                Serial.printf("OTA Progress: %u%% (%d/%d bytes)\n", percentage, cur, total);
                lastProgressTime = now;
            }
        });

        ESPhttpUpdate.onError([](int error) {
            Serial.printf("OTA Error[%u]: %s\n", error, ESPhttpUpdate.getLastErrorString().c_str());
        });

        // Use optimized WiFi client
        WiFiClient updateClient;

        // Perform the update
        Serial.println("Downloading firmware...");
        unsigned long startTime = millis();
        t_httpUpdate_return result = ESPhttpUpdate.update(updateClient, firmwareUrl);
        unsigned long updateTime = millis() - startTime;

        switch (result)
        {
        case HTTP_UPDATE_FAILED:
            Serial.printf("OTA Update failed after %lu ms. Error (%d): %s\n",
                          updateTime, ESPhttpUpdate.getLastError(), ESPhttpUpdate.getLastErrorString().c_str());
            break;

        case HTTP_UPDATE_NO_UPDATES:
            Serial.printf("OTA: No updates available (checked in %lu ms)\n", updateTime);
            break;

        case HTTP_UPDATE_OK:
            Serial.printf("OTA Update successful in %lu ms (%.1f seconds)! Restarting...\n",
                          updateTime, updateTime / 1000.0);
            delay(1000); // Brief delay before restart
            ESP.restart();
            break;
        }
    }
};

#endif // OTA_MANAGER_H
