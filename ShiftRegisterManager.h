#ifndef SHIFT_REGISTER_MANAGER_H
#define SHIFT_REGISTER_MANAGER_H

#include <Arduino.h>

class ShiftRegisterManager
{
private:
    uint8_t _dataPin;
    uint8_t _clockPin;
    uint8_t _latchPin;
    uint16_t _outputState;

    // Hardware-specific bit mapping for ESP8266 dual shift register setup (3-Relay variant)
    // Data structure: x1x2x3x4x5x6x7x8 x9x10x11x12x13x14x15x16
    // Transmission order: x9x10x11x12x13x14x15x16x1x2x3x4x5x6x7x8
    //
    // Based on hardware test results:
    // Bit 0 (x1) -> none           Bit 8 (x9)  -> none
    // Bit 1 (x2) -> r1             Bit 9 (x10) -> Relay3
    // Bit 2 (x3) -> b1             Bit 10 (x11) -> Relay2
    // Bit 3 (x4) -> g1             Bit 11 (x12) -> Relay1
    // Bit 4 (x5) -> r2             Bit 12 (x13) -> r3
    // Bit 5 (x6) -> b2             Bit 13 (x14) -> b3
    // Bit 6 (x7) -> g2             Bit 14 (x15) -> g3
    // Bit 7 (x8) -> none           Bit 15 (x16) -> none

    // Bit position arrays for hardware mapping (3-Relay variant)
    const uint8_t RELAY_BITS[3] = {11, 10, 9}; // R1, R2, R3 bit positions (0-indexed)
    const uint8_t RED_BITS[3] = {1, 4, 12};    // r1, r2, r3 bit positions
    const uint8_t GREEN_BITS[3] = {3, 6, 14};  // g1, g2, g3 bit positions
    const uint8_t BLUE_BITS[3] = {2, 5, 13};   // b1, b2, b3 bit positions

    // Update the physical shift registers with current state
    void updateShiftRegisters()
    {
        digitalWrite(_latchPin, LOW);

        // Send bits 9-16 first (goes to second 74HC595)
        shiftOut(_dataPin, _clockPin, MSBFIRST, (_outputState >> 8) & 0xFF);

        // Send bits 1-8 second (stays in first 74HC595)
        shiftOut(_dataPin, _clockPin, MSBFIRST, _outputState & 0xFF);

        digitalWrite(_latchPin, HIGH);
    }

public:
    ShiftRegisterManager(uint8_t dataPin, uint8_t clockPin, uint8_t latchPin)
        : _dataPin(dataPin), _clockPin(clockPin), _latchPin(latchPin), _outputState(0)
    {
    }

    // Initialize the shift register pins
    void begin()
    {
        pinMode(_dataPin, OUTPUT);
        pinMode(_clockPin, OUTPUT);
        pinMode(_latchPin, OUTPUT);

        // Initialize all outputs to LOW
        _outputState = 0;
        updateShiftRegisters();

        Serial.println("3-Relay Shift Register Manager initialized");
        Serial.print("Data Pin: ");
        Serial.println(_dataPin);
        Serial.print("Clock Pin: ");
        Serial.println(_clockPin);
        Serial.print("Latch Pin: ");
        Serial.println(_latchPin);
    }

    // Set relay state (relayIndex: 0-2 for relays 1-3)
    void setRelay(uint8_t relayIndex, bool state)
    {
        if (relayIndex >= 3)
            return;

        uint8_t bitPos = RELAY_BITS[relayIndex];

        if (state)
        {
            _outputState |= (1 << bitPos);
        }
        else
        {
            _outputState &= ~(1 << bitPos);
        }

        updateShiftRegisters();

        Serial.print("3-Relay ");
        Serial.print(relayIndex + 1);
        Serial.print(" set to ");
        Serial.println(state ? "ON" : "OFF");
    }

    // Get relay state
    bool getRelay(uint8_t relayIndex)
    {
        if (relayIndex >= 3)
            return false;

        uint8_t bitPos = RELAY_BITS[relayIndex];
        return (_outputState & (1 << bitPos)) != 0;
    }

    // Set RGB color for a specific relay button (relayIndex: 0-2 for relays 1-3)
    // Note: RGB values are digital (0=OFF, >0=ON) since 74HC595 provides digital outputs only
    void setRGB(uint8_t relayIndex, uint8_t r, uint8_t g, uint8_t b)
    {
        if (relayIndex >= 3)
            return;

        // Set Red (digital: 0=OFF, >0=ON)
        if (r > 0)
        {
            _outputState |= (1 << RED_BITS[relayIndex]);
        }
        else
        {
            _outputState &= ~(1 << RED_BITS[relayIndex]);
        }

        // Set Green (digital: 0=OFF, >0=ON)
        if (g > 0)
        {
            _outputState |= (1 << GREEN_BITS[relayIndex]);
        }
        else
        {
            _outputState &= ~(1 << GREEN_BITS[relayIndex]);
        }

        // Set Blue (digital: 0=OFF, >0=ON)
        if (b > 0)
        {
            _outputState |= (1 << BLUE_BITS[relayIndex]);
        }
        else
        {
            _outputState &= ~(1 << BLUE_BITS[relayIndex]);
        }

        updateShiftRegisters();

        Serial.print("3-Relay RGB for Relay ");
        Serial.print(relayIndex + 1);
        Serial.print(" set to R:");
        Serial.print(r > 0 ? "ON" : "OFF");
        Serial.print(" G:");
        Serial.print(g > 0 ? "ON" : "OFF");
        Serial.print(" B:");
        Serial.println(b > 0 ? "ON" : "OFF");
    }

    // Get RGB color for a specific relay button
    void getRGB(uint8_t relayIndex, uint8_t &r, uint8_t &g, uint8_t &b)
    {
        if (relayIndex >= 3)
        {
            r = g = b = 0;
            return;
        }

        r = (_outputState & (1 << RED_BITS[relayIndex])) ? 255 : 0;
        g = (_outputState & (1 << GREEN_BITS[relayIndex])) ? 255 : 0;
        b = (_outputState & (1 << BLUE_BITS[relayIndex])) ? 255 : 0;
    }

    // Set individual output bit (for advanced control)
    void setOutput(uint8_t bitIndex, bool state)
    {
        if (bitIndex >= 16)
            return;

        if (state)
        {
            _outputState |= (1 << bitIndex);
        }
        else
        {
            _outputState &= ~(1 << bitIndex);
        }

        updateShiftRegisters();
    }

    // Get individual output bit state
    bool getOutput(uint8_t bitIndex)
    {
        if (bitIndex >= 16)
            return false;
        return (_outputState & (1 << bitIndex)) != 0;
    }

    // Set all outputs at once
    void setAllOutputs(uint16_t state)
    {
        _outputState = state;
        updateShiftRegisters();
    }

    // Get current output state
    uint16_t getAllOutputs()
    {
        return _outputState;
    }

    // Clear all outputs
    void clearAll()
    {
        _outputState = 0;
        updateShiftRegisters();
        Serial.println("All 3-Relay shift register outputs cleared");
    }
};

#endif // SHIFT_REGISTER_MANAGER_H
