# OTA Update Test Implementation

This folder contains a complete OTA (Over-The-Air) update test implementation for ESP8266 devices.

## Structure

```
test/
├── OTA_Test.ino           # Arduino code for ESP8266
├── build/                 # Firmware files directory
├── server/                # Python OTA server
│   ├── ota_server.py      # Main server script
│   ├── update_info.json   # Update configuration
│   ├── requirements.txt   # Python dependencies
│   └── README.md          # This file
└── README.md
```

## Setup Instructions

### 1. Arduino Code Setup

1. Open `OTA_Test.ino` in Arduino IDE
2. Update WiFi credentials in the code:
   ```cpp
   const char* WIFI_SSID = "YourWiFiSSID";
   const char* WIFI_PASSWORD = "YourWiFiPassword";
   ```
3. Update the server IP address to your laptop's IP:
   ```cpp
   const char* OTA_SERVER_HOST = "*************"; // Change to your laptop's IP
   ```
4. Install required libraries:
   - ESP8266WiFi (included with ESP8266 board package)
   - ESP8266HTTPClient (included with ESP8266 board package)
   - ESP8266httpUpdate (included with ESP8266 board package)
   - ArduinoJson (install from Library Manager)

### 2. Python Server Setup

1. Navigate to the `server` directory
2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the server:
   ```bash
   python ota_server.py
   ```

The server will start on `http://0.0.0.0:8080`

### 3. Firmware Files

Place your compiled firmware files in the `build/` directory. The firmware files should be named according to the `firmwareFile` field in `update_info.json`.

For example:
- `ESP8266_Switch_v1.1.0.bin`
- `ESP8266_Switch_4Relay_v1.1.0.bin`

## How It Works

### Device Side (ESP8266)

1. **WiFi Connection**: Device connects to WiFi using preset credentials
2. **Update Check**: Every 12 hours (configurable), device sends POST request to server with:
   ```json
   {
     "model": "ESP8266_Switch",
     "version": "1.0.0"
   }
   ```
3. **Server Response**: Server responds with update availability:
   ```json
   {
     "updateAvailable": true,
     "version": "1.1.0",
     "releaseNotes": "Bug fixes and improvements"
   }
   ```
4. **OTA Update**: If update is available, device downloads and installs firmware

### Server Side (Python)

1. **Update Check Endpoint** (`/check-update`): Compares device version with available version
2. **Firmware Download** (`/download-firmware`): Serves firmware files
3. **Configuration**: Update information stored in `update_info.json`

## API Endpoints

- `POST /check-update` - Check for available updates
- `GET /download-firmware` - Download firmware file
- `GET /status` - Server status
- `GET /update-info` - Get current update configuration
- `POST /update-info` - Update configuration (for testing)

## Configuration

Edit `update_info.json` to configure available updates:

```json
{
  "ESP8266_Switch": {
    "version": "1.1.0",
    "releaseDate": "2025-07-07",
    "firmwareFile": "ESP8266_Switch_v1.1.0.bin",
    "firmwareSize": 512000,
    "releaseNotes": "Bug fixes and improvements",
    "mandatory": false,
    "minRequiredVersion": "1.0.0"
  }
}
```

## Testing

1. Upload the Arduino code to your ESP8266
2. Start the Python server
3. Monitor serial output for update checks
4. Place a newer firmware file in `build/` directory
5. Update the version in `update_info.json`
6. Device will detect and install the update on next check

## Notes

- The device checks for updates every 12 hours by default
- Update check also happens on startup
- Server logs all update requests and responses
- Firmware files must be placed in the `build/` directory
- Update your laptop's IP address in the Arduino code
- Ensure both device and laptop are on the same network
