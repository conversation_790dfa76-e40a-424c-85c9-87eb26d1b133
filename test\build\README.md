# Firmware Build Directory

Place your compiled firmware files (.bin) in this directory.

## Expected Files

Based on the current `update_info.json` configuration, the following firmware files are expected:

- `ESP8266_Switch_v1.1.0.bin` - Main switch firmware v1.1.0
- `ESP8266_Switch_4Relay_v1.1.0.bin` - 4-relay variant firmware v1.1.0

## How to Generate Firmware Files

1. Compile your Arduino sketch in Arduino IDE
2. Go to Sketch → Export compiled Binary
3. The .bin file will be saved in your sketch folder
4. Copy the .bin file to this directory
5. Rename it to match the `firmwareFile` name in `update_info.json`

## File Naming Convention

Use the format: `{ModelName}_v{Version}.bin`

Examples:
- `ESP8266_Switch_v1.1.0.bin`
- `ESP8266_Switch_v1.2.0.bin`
- `ESP8266_Switch_4Relay_v1.1.0.bin`

## Important Notes

- Firmware files must be compiled for the correct ESP8266 board variant
- File names must exactly match the `firmwareFile` field in `update_info.json`
- File sizes should be reasonable for ESP8266 (typically 300KB - 1MB)
- Always test firmware files before deploying them via OTA
