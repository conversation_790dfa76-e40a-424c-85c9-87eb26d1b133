#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#include <ESP8266httpUpdate.h>
#include <ArduinoJson.h>
#include <WiFiClient.h>

// WiFi credentials (preset constants)
const char *WIFI_SSID = "hirmand";
const char *WIFI_PASSWORD = "p@ssword_ADMIN";

// Device information
const char *DEVICE_MODEL = "ESP8266_Switch";
const char *CURRENT_VERSION = "1.1.0";

// OTA Server configuration
const char *OTA_SERVER_HOST = "***********"; // Change to your laptop's IP
const int OTA_SERVER_PORT = 8080;
const char *UPDATE_CHECK_ENDPOINT = "/check-update";
const char *FIRMWARE_DOWNLOAD_ENDPOINT = "/download-firmware";

// Update check interval (12 hours in milliseconds)
const unsigned long UPDATE_CHECK_INTERVAL = 12 * 60 * 60 * 1000;
unsigned long lastUpdateCheck = 0;

WiFiClient wifiClient;
HTTPClient httpClient;

void setup()
{
  Serial.begin(115200);
  Serial.println();
  Serial.println("=== OTA Test Starting ===");

  // Connect to WiFi
  connectToWiFi();

  // Initial update check
  checkForUpdates();

  Serial.println("=== Setup Complete ===");
}

void loop()
{
  // Check WiFi connection
  if (WiFi.status() != WL_CONNECTED)
  {
    Serial.println("WiFi disconnected, reconnecting...");
    connectToWiFi();
  }

  // Check for updates every 12 hours
  if (millis() - lastUpdateCheck >= UPDATE_CHECK_INTERVAL)
  {
    checkForUpdates();
  }

  // Simple heartbeat
  static unsigned long lastHeartbeat = 0;
  if (millis() - lastHeartbeat >= 30000)
  { // Every 30 seconds
    Serial.println("Device running... Current version: " + String(CURRENT_VERSION));
    lastHeartbeat = millis();
  }

  delay(1000);
}

void connectToWiFi()
{
  Serial.print("Connecting to WiFi: ");
  Serial.println(WIFI_SSID);

  // Optimize WiFi settings for better performance
  WiFi.mode(WIFI_STA);
  WiFi.setAutoConnect(true);
  WiFi.setAutoReconnect(true);

  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 30)
  {
    delay(1000);
    Serial.print(".");
    attempts++;
  }

  if (WiFi.status() == WL_CONNECTED)
  {
    Serial.println();
    Serial.println("WiFi connected successfully!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
    Serial.print("Signal strength: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
  }
  else
  {
    Serial.println();
    Serial.println("Failed to connect to WiFi!");
  }
}

void checkForUpdates()
{
  if (WiFi.status() != WL_CONNECTED)
  {
    Serial.println("Cannot check for updates - WiFi not connected");
    return;
  }

  Serial.println("Checking for updates...");
  lastUpdateCheck = millis();

  // Prepare JSON payload
  DynamicJsonDocument requestDoc(256);
  requestDoc["model"] = DEVICE_MODEL;
  requestDoc["version"] = CURRENT_VERSION;

  String requestBody;
  serializeJson(requestDoc, requestBody);

  // Make HTTP POST request
  String url = "http://" + String(OTA_SERVER_HOST) + ":" + String(OTA_SERVER_PORT) + UPDATE_CHECK_ENDPOINT;

  Serial.println("Request URL: " + url);
  Serial.println("Request body: " + requestBody);

  httpClient.begin(wifiClient, url);
  httpClient.addHeader("Content-Type", "application/json");
  httpClient.setTimeout(10000); // 10 second timeout

  Serial.println("Sending POST request...");
  int httpResponseCode = httpClient.POST(requestBody);
  Serial.println("HTTP Response Code: " + String(httpResponseCode));

  if (httpResponseCode > 0)
  {
    String response = httpClient.getString();
    Serial.println("Server response: " + response);

    // Parse response
    DynamicJsonDocument responseDoc(512);
    DeserializationError error = deserializeJson(responseDoc, response);

    if (!error)
    {
      bool updateAvailable = responseDoc["updateAvailable"];

      if (updateAvailable)
      {
        String newVersion = responseDoc["version"];
        String releaseNotes = responseDoc["releaseNotes"];

        Serial.println("Update available!");
        Serial.println("New version: " + newVersion);
        Serial.println("Release notes: " + releaseNotes);

        // Perform OTA update
        performOTAUpdate();
      }
      else
      {
        Serial.println("No updates available");
      }
    }
    else
    {
      Serial.println("Failed to parse server response");
    }
  }
  else
  {
    Serial.println("HTTP request failed with code: " + String(httpResponseCode));
  }

  httpClient.end();
}

void performOTAUpdate()
{
  Serial.println("Starting OTA update...");

  String firmwareUrl = "http://" + String(OTA_SERVER_HOST) + ":" + String(OTA_SERVER_PORT) + FIRMWARE_DOWNLOAD_ENDPOINT;

  // Configure update client with optimizations
  ESPhttpUpdate.setLedPin(LED_BUILTIN, LOW);

  // Set timeouts for faster operation
  ESPhttpUpdate.setTimeout(8000); // 8 second timeout instead of default
  ESPhttpUpdate.setFollowRedirects(HTTPC_STRICT_FOLLOW_REDIRECTS);

  ESPhttpUpdate.onStart([]()
                        {
    Serial.println("OTA update started");
    Serial.println("This should take 30-60 seconds..."); });

  ESPhttpUpdate.onEnd([]()
                      { Serial.println("OTA update finished successfully!"); });

  ESPhttpUpdate.onProgress([](int cur, int total)
                           {
    static unsigned long lastProgressTime = 0;
    unsigned long now = millis();

    // Update progress every 2 seconds to avoid spam
    if (now - lastProgressTime > 2000 || cur == total) {
      int percentage = (cur * 100) / total;
      Serial.printf("OTA Progress: %u%% (%d/%d bytes)\n", percentage, cur, total);
      lastProgressTime = now;
    } });

  ESPhttpUpdate.onError([](int error)
                        { Serial.printf("OTA Error[%u]: %s\n", error, ESPhttpUpdate.getLastErrorString().c_str()); });

  // Use optimized WiFi client
  WiFiClient updateClient;
  updateClient.setTimeout(8000); // 8 second timeout

  // Perform the update
  Serial.println("Downloading firmware...");
  unsigned long startTime = millis();
  t_httpUpdate_return result = ESPhttpUpdate.update(updateClient, firmwareUrl);
  unsigned long updateTime = millis() - startTime;

  switch (result)
  {
  case HTTP_UPDATE_FAILED:
    Serial.printf("OTA Update failed after %lu ms. Error (%d): %s\n",
                  updateTime, ESPhttpUpdate.getLastError(), ESPhttpUpdate.getLastErrorString().c_str());
    break;

  case HTTP_UPDATE_NO_UPDATES:
    Serial.printf("OTA: No updates available (checked in %lu ms)\n", updateTime);
    break;

  case HTTP_UPDATE_OK:
    Serial.printf("OTA Update successful in %lu ms (%.1f seconds)! Restarting...\n",
                  updateTime, updateTime / 1000.0);
    delay(1000); // Brief delay before restart
    ESP.restart();
    break;
  }
}
