#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#include <ESP8266httpUpdate.h>
#include <ArduinoJson.h>
#include <WiFiClient.h>

// WiFi credentials (preset constants)
const char* WIFI_SSID = "hirmand";
const char* WIFI_PASSWORD = "p@ssword_ADMIN";

// Device information
const char* DEVICE_MODEL = "ESP8266_Switch";
const char* CURRENT_VERSION = "1.0.0";

// OTA Server configuration
const char* OTA_SERVER_HOST = "***********"; // Change to your laptop's IP
const int OTA_SERVER_PORT = 8080;
const char* UPDATE_CHECK_ENDPOINT = "/check-update";
const char* FIRMWARE_DOWNLOAD_ENDPOINT = "/download-firmware";

// Update check interval (12 hours in milliseconds)
const unsigned long UPDATE_CHECK_INTERVAL = 12 * 60 * 60 * 1000;
unsigned long lastUpdateCheck = 0;

WiFiClient wifiClient;
HTTPClient httpClient;

void setup() {
  Serial.begin(115200);
  Serial.println();
  Serial.println("=== OTA Test Starting ===");
  
  // Connect to WiFi
  connectToWiFi();
  
  // Initial update check
  checkForUpdates();
  
  Serial.println("=== Setup Complete ===");
}

void loop() {
  // Check WiFi connection
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi disconnected, reconnecting...");
    connectToWiFi();
  }
  
  // Check for updates every 12 hours
  if (millis() - lastUpdateCheck >= UPDATE_CHECK_INTERVAL) {
    checkForUpdates();
  }
  
  // Simple heartbeat
  static unsigned long lastHeartbeat = 0;
  if (millis() - lastHeartbeat >= 30000) { // Every 30 seconds
    Serial.println("Device running... Current version: " + String(CURRENT_VERSION));
    lastHeartbeat = millis();
  }
  
  delay(1000);
}

void connectToWiFi() {
  Serial.print("Connecting to WiFi: ");
  Serial.println(WIFI_SSID);
  
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 30) {
    delay(1000);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.println("WiFi connected successfully!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println();
    Serial.println("Failed to connect to WiFi!");
  }
}

void checkForUpdates() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("Cannot check for updates - WiFi not connected");
    return;
  }
  
  Serial.println("Checking for updates...");
  lastUpdateCheck = millis();
  
  // Prepare JSON payload
  DynamicJsonDocument requestDoc(256);
  requestDoc["model"] = DEVICE_MODEL;
  requestDoc["version"] = CURRENT_VERSION;
  
  String requestBody;
  serializeJson(requestDoc, requestBody);
  
  // Make HTTP POST request
  String url = "http://" + String(OTA_SERVER_HOST) + ":" + String(OTA_SERVER_PORT) + UPDATE_CHECK_ENDPOINT;
  
  httpClient.begin(wifiClient, url);
  httpClient.addHeader("Content-Type", "application/json");
  
  int httpResponseCode = httpClient.POST(requestBody);
  
  if (httpResponseCode > 0) {
    String response = httpClient.getString();
    Serial.println("Server response: " + response);
    
    // Parse response
    DynamicJsonDocument responseDoc(512);
    DeserializationError error = deserializeJson(responseDoc, response);
    
    if (!error) {
      bool updateAvailable = responseDoc["updateAvailable"];
      
      if (updateAvailable) {
        String newVersion = responseDoc["version"];
        String releaseNotes = responseDoc["releaseNotes"];
        
        Serial.println("Update available!");
        Serial.println("New version: " + newVersion);
        Serial.println("Release notes: " + releaseNotes);
        
        // Perform OTA update
        performOTAUpdate();
      } else {
        Serial.println("No updates available");
      }
    } else {
      Serial.println("Failed to parse server response");
    }
  } else {
    Serial.println("HTTP request failed with code: " + String(httpResponseCode));
  }
  
  httpClient.end();
}

void performOTAUpdate() {
  Serial.println("Starting OTA update...");
  
  String firmwareUrl = "http://" + String(OTA_SERVER_HOST) + ":" + String(OTA_SERVER_PORT) + FIRMWARE_DOWNLOAD_ENDPOINT;
  
  // Configure update client
  ESPhttpUpdate.setLedPin(LED_BUILTIN, LOW);
  ESPhttpUpdate.onStart([]() {
    Serial.println("OTA update started");
  });
  
  ESPhttpUpdate.onEnd([]() {
    Serial.println("OTA update finished");
  });
  
  ESPhttpUpdate.onProgress([](int cur, int total) {
    Serial.printf("OTA Progress: %u%%\n", (cur * 100) / total);
  });
  
  ESPhttpUpdate.onError([](int error) {
    Serial.printf("OTA Error[%u]: %s\n", error, ESPhttpUpdate.getLastErrorString().c_str());
  });
  
  // Perform the update
  WiFiClient updateClient;
  t_httpUpdate_return result = ESPhttpUpdate.update(updateClient, firmwareUrl);
  
  switch (result) {
    case HTTP_UPDATE_FAILED:
      Serial.printf("OTA Update failed. Error (%d): %s\n", ESPhttpUpdate.getLastError(), ESPhttpUpdate.getLastErrorString().c_str());
      break;
      
    case HTTP_UPDATE_NO_UPDATES:
      Serial.println("OTA: No updates available");
      break;
      
    case HTTP_UPDATE_OK:
      Serial.println("OTA Update successful! Restarting...");
      ESP.restart();
      break;
  }
}
