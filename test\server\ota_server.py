#!/usr/bin/env python3
"""
OTA Update Server for ESP8266 devices
Handles update checks and firmware distribution
"""

import json
import os
from flask import Flask, request, jsonify, send_file
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
FIRMWARE_DIR = "../build"
UPDATE_INFO_FILE = "update_info.json"

def load_update_info():
    """Load update information from JSON file"""
    try:
        with open(UPDATE_INFO_FILE, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"Update info file {UPDATE_INFO_FILE} not found")
        return {}
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in {UPDATE_INFO_FILE}")
        return {}

def save_update_info(data):
    """Save update information to JSON file"""
    try:
        with open(UPDATE_INFO_FILE, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Failed to save update info: {e}")
        return False

def compare_versions(current, available):
    """Compare version strings (simple semantic versioning)"""
    try:
        current_parts = [int(x) for x in current.split('.')]
        available_parts = [int(x) for x in available.split('.')]
        
        # Pad shorter version with zeros
        max_len = max(len(current_parts), len(available_parts))
        current_parts.extend([0] * (max_len - len(current_parts)))
        available_parts.extend([0] * (max_len - len(available_parts)))
        
        return available_parts > current_parts
    except ValueError:
        logger.error(f"Invalid version format: current={current}, available={available}")
        return False

@app.route('/check-update', methods=['POST'])
def check_update():
    """Handle update check requests from devices"""
    try:
        # Parse request
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON"}), 400
        
        device_model = data.get('model')
        current_version = data.get('version')
        
        if not device_model or not current_version:
            return jsonify({"error": "Missing model or version"}), 400
        
        logger.info(f"Update check from {device_model} v{current_version}")
        
        # Load update information
        update_info = load_update_info()
        
        # Check if update is available for this model
        model_info = update_info.get(device_model, {})
        available_version = model_info.get('version')
        
        if not available_version:
            logger.info(f"No update info available for model {device_model}")
            return jsonify({
                "updateAvailable": False,
                "message": "No update information available for this model"
            })
        
        # Compare versions
        update_available = compare_versions(current_version, available_version)
        
        response = {
            "updateAvailable": update_available,
            "timestamp": datetime.now().isoformat()
        }
        
        if update_available:
            response.update({
                "version": available_version,
                "releaseNotes": model_info.get('releaseNotes', 'No release notes available'),
                "firmwareSize": model_info.get('firmwareSize', 0),
                "releaseDate": model_info.get('releaseDate', '')
            })
            logger.info(f"Update available: {current_version} -> {available_version}")
        else:
            logger.info(f"No update needed for {device_model} v{current_version}")
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error processing update check: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/download-firmware', methods=['GET'])
def download_firmware():
    """Serve firmware file for OTA update"""
    try:
        # Get device model from query parameter or use default
        device_model = request.args.get('model', 'ESP8266_Switch')
        
        # Load update info to get firmware filename
        update_info = load_update_info()
        model_info = update_info.get(device_model, {})
        firmware_filename = model_info.get('firmwareFile', 'firmware.bin')
        
        firmware_path = os.path.join(FIRMWARE_DIR, firmware_filename)
        
        if not os.path.exists(firmware_path):
            logger.error(f"Firmware file not found: {firmware_path}")
            return jsonify({"error": "Firmware file not found"}), 404
        
        logger.info(f"Serving firmware: {firmware_path}")
        return send_file(firmware_path, as_attachment=True, download_name=firmware_filename)
        
    except Exception as e:
        logger.error(f"Error serving firmware: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/status', methods=['GET'])
def status():
    """Server status endpoint"""
    update_info = load_update_info()
    return jsonify({
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "available_models": list(update_info.keys()),
        "firmware_directory": FIRMWARE_DIR
    })

@app.route('/update-info', methods=['GET'])
def get_update_info():
    """Get current update information"""
    return jsonify(load_update_info())

@app.route('/update-info', methods=['POST'])
def set_update_info():
    """Update the update information (for testing/management)"""
    try:
        data = request.get_json()
        if save_update_info(data):
            return jsonify({"message": "Update info saved successfully"})
        else:
            return jsonify({"error": "Failed to save update info"}), 500
    except Exception as e:
        logger.error(f"Error updating info: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # Create firmware directory if it doesn't exist
    os.makedirs(FIRMWARE_DIR, exist_ok=True)
    
    # Start server
    logger.info("Starting OTA Update Server...")
    logger.info(f"Firmware directory: {os.path.abspath(FIRMWARE_DIR)}")
    logger.info(f"Update info file: {os.path.abspath(UPDATE_INFO_FILE)}")
    
    app.run(host='0.0.0.0', port=8080, debug=True)
